"""
Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
Mostly copy-paste from https://github.com/pytorch/vision/blob/13b35ff/references/detection/coco_utils.py

Copyright(c) 2023 lyuwenyu. All Rights Reserved.
"""

import torch
import torch.utils.data
from typing import List, Dict, Optional, Union
import torchvision
torchvision.disable_beta_transforms_warning()

from pathlib import Path
import json

from PIL import Image 
from pycocotools import mask as coco_mask

from ._dataset import DetDataset
from torch.utils.data import Dataset
from .._misc import convert_to_tv_tensor
from ...core import register

__all__ = ['DefectJSONDetection']

def _clip_xyxy(b, W, H):
    x1, y1, x2, y2 = map(float, b)
    x1 = max(0.0, min(x1, W - 1))
    y1 = max(0.0, min(y1, H - 1))
    x2 = max(0.0, min(x2, W - 1))
    y2 = max(0.0, min(y2, H - 1))
    if x2 < x1: x1, x2 = x2, x1
    if y2 < y1: y1, y2 = y2, y1
    return [x1, y1, x2, y2]


@register()
class DefectJSONDetection(DetDataset, Dataset):
    __inject__ = ['transforms', ]
    __share__ = []
    
    def __init__(
        self,
        images: List[Union[str, Path]],
        json_dir: Union[str, Path],
        transforms=None,
        class_id_map: Optional[Dict[int, int]] = None,
    ):
        self.images = list(Path(images).glob("*.jpg")) + list(Path(images).glob("*.png"))
        self.json_dir = Path(json_dir)
        self._transforms = transforms
        self.class_id_map = class_id_map

    def __len__(self): 
        return len(self.images)
    
    def __getitem__(self, idx):
        img, target = self.load_item(idx)
        if self._transforms is not None:
            img, target, _ = self._transforms(img, target, self)
        return img, target

    def load_item(self, idx):
        img_path = self.images[idx]
        img = Image.open(img_path).convert("RGB")
        W, H = img.size

        jpath = self.json_dir / f"{img_path.stem}.json"
        if not jpath.exists():
            raise FileNotFoundError(f"No existe anotación: {jpath}")
        with jpath.open("r", encoding="utf-8") as f:
            data = json.load(f)

        # clave: exacta o por stem (por si cambia la extensión)
        if img_path.name in data:
            key = img_path.name
        else:
            key = next((k for k in data.keys() if Path(k).stem == img_path.stem), None)
            if key is None:
                raise KeyError(f"'{img_path.name}' no está en {jpath}. Claves: {list(data.keys())[:5]}")

        ann = data[key]
        boxes = ann.get("boxes", []) or []
        labels = ann.get("labels", []) or []

        assert len(boxes) > 0, f"No hay cajas en {jpath} para {key}"
        assert len(boxes) == len(labels), f"Distinto nº de cajas y etiquetas"
        # clip + filtrado de cajas degeneradas
        boxes_c = []
        labels_c = []
        for b, l in zip(boxes, labels):
            x1, y1, x2, y2 = _clip_xyxy(b, W, H)
            if (x2 - x1) > 0 and (y2 - y1) > 0:
                boxes_c.append([x1, y1, x2, y2])
                labels_c.append(int(l))

        # remapeo opcional a 0..K-1
        if self.class_id_map is not None:
            labels_c = [int(self.class_id_map[l]) for l in labels_c]

        if boxes_c:
            boxes_t = torch.tensor(boxes_c, dtype=torch.float32)
            labels_t = torch.tensor(labels_c, dtype=torch.long)
            area = (boxes_t[:, 2] - boxes_t[:, 0]) * (boxes_t[:, 3] - boxes_t[:, 1])
            iscrowd = torch.zeros((boxes_t.size(0),), dtype=torch.int64)
        else:
            boxes_t = torch.zeros((0, 4), dtype=torch.float32)
            labels_t = torch.zeros((0,), dtype=torch.long)
            area = torch.zeros((0,), dtype=torch.float32)
            iscrowd = torch.zeros((0,), dtype=torch.int64)

        target = {
            "boxes": convert_to_tv_tensor(boxes_t, key="boxes", spatial_size=(H, W)) ,
            "labels": labels_t,
            "image_id": torch.tensor([idx], dtype=torch.int64),
            "idx": torch.tensor([idx], dtype=torch.int64),
            "area": area,
            "iscrowd": iscrowd,
            "orig_size": torch.as_tensor([W, H], dtype=torch.int64),
            # "size": torch.as_tensor([W, H], dtype=torch.int64),  # descomenta si tu loss lo usa
        }
        return img, target

    # def extra_repr(self) -> str:
    #     s = f' img_folder: {self.img_folder}\n ann_file: {self.ann_file}\n'
    #     s += f' return_masks: {self.return_masks}\n'
    #     if hasattr(self, '_transforms') and self._transforms is not None:
    #         s += f' transforms:\n   {repr(self._transforms)}'
    #     if hasattr(self, '_preset') and self._preset is not None:
    #         s += f' preset:\n   {repr(self._preset)}'
    #     return s 

    # @property
    # def categories(self, ):
    #     return self.coco.dataset['categories']

    # @property
    # def category2name(self, ):
    #     return {cat['id']: cat['name'] for cat in self.categories}

    # @property
    # def category2label(self, ):
    #     return {cat['id']: i for i, cat in enumerate(self.categories)}

    # @property
    # def label2category(self, ):
    #     return {i: cat['id'] for i, cat in enumerate(self.categories)}



# mscoco_category2name = {
#     1: 'person',
#     2: 'bicycle',
#     3: 'car',
#     4: 'motorcycle',
#     5: 'airplane',
#     6: 'bus',
#     7: 'train',
#     8: 'truck',
#     9: 'boat',
#     10: 'traffic light',
#     11: 'fire hydrant',
#     13: 'stop sign',
#     14: 'parking meter',
#     15: 'bench',
#     16: 'bird',
#     17: 'cat',
#     18: 'dog',
#     19: 'horse',
#     20: 'sheep',
#     21: 'cow',
#     22: 'elephant',
#     23: 'bear',
#     24: 'zebra',
#     25: 'giraffe',
#     27: 'backpack',
#     28: 'umbrella',
#     31: 'handbag',
#     32: 'tie',
#     33: 'suitcase',
#     34: 'frisbee',
#     35: 'skis',
#     36: 'snowboard',
#     37: 'sports ball',
#     38: 'kite',
#     39: 'baseball bat',
#     40: 'baseball glove',
#     41: 'skateboard',
#     42: 'surfboard',
#     43: 'tennis racket',
#     44: 'bottle',
#     46: 'wine glass',
#     47: 'cup',
#     48: 'fork',
#     49: 'knife',
#     50: 'spoon',
#     51: 'bowl',
#     52: 'banana',
#     53: 'apple',
#     54: 'sandwich',
#     55: 'orange',
#     56: 'broccoli',
#     57: 'carrot',
#     58: 'hot dog',
#     59: 'pizza',
#     60: 'donut',
#     61: 'cake',
#     62: 'chair',
#     63: 'couch',
#     64: 'potted plant',
#     65: 'bed',
#     67: 'dining table',
#     70: 'toilet',
#     72: 'tv',
#     73: 'laptop',
#     74: 'mouse',
#     75: 'remote',
#     76: 'keyboard',
#     77: 'cell phone',
#     78: 'microwave',
#     79: 'oven',
#     80: 'toaster',
#     81: 'sink',
#     82: 'refrigerator',
#     84: 'book',
#     85: 'clock',
#     86: 'vase',
#     87: 'scissors',
#     88: 'teddy bear',
#     89: 'hair drier',
#     90: 'toothbrush'
# }

# mscoco_category2label = {k: i for i, k in enumerate(mscoco_category2name.keys())}
# mscoco_label2category = {v: k for k, v in mscoco_category2label.items()}
