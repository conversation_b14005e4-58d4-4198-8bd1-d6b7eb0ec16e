# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# COCO evaluator that works in distributed mode + TorchMetrics mAP
# Mostly copy-paste from https://github.com/pytorch/vision/blob/edfd5a7/references/detection/coco_eval.py

import os
import contextlib
import copy
import numpy as np
import torch

from pycocotools.cocoeval import COCOeval
from pycocotools.coco import COCO
import pycocotools.mask as mask_util

from torchmetrics.detection.mean_ap import MeanAveragePrecision  # <-- NEW

from ...misc import dist_utils
from ...core import register

__all__ = ['TextileEvaluator',]


@register()
class TextileEvaluator(object):
    def __init__(self, coco_gt, iou_types, iou_thresholds=[0.4]):
        assert isinstance(iou_types, (list, tuple))
        coco_gt = copy.deepcopy(coco_gt)
        self.coco_gt = coco_gt
        self.iou_types = iou_types
        self._tm_iou_thresholds = iou_thresholds  # <-- NEW

        # pycocotools evaluators (unchanged)
        self.coco_eval = {}
        for iou_type in iou_types:
            self.coco_eval[iou_type] = COCOeval(coco_gt, iouType=iou_type)
            # self.coco_eval[iou_type].params.iouThrs = np.array([0.4])  # example customization

        # TorchMetrics mAP for bbox and segm (segm optional; see note below)
        # box_format must match your predictions ('xyxy' here).
        self._tm_bbox = MeanAveragePrecision(
            iou_type="bbox",
            iou_thresholds=iou_thresholds,
            box_format="xyxy",
            class_metrics=True   # <-- enable per-class outputs
        )
        # If you also want mask AP, uncomment the next line AND feed prediction["masks"] as [N, H, W] bool/0-1
        # self._tm_segm = MeanAveragePrecision(iou_type="segm")

        self.img_ids = []
        self.eval_imgs = {k: [] for k in iou_types}

        # caches for torchmetrics targets/preds per image (gathered across procs)
        self._tm_preds = {}   # img_id -> prediction dict (bbox or segm fields)
        self._tm_tgts  = {}   # img_id -> target dict built from COCO GT

    def _coco_classes_lookup(self):
        # Returns dict[int_id] -> str_name for pretty printing
        cats = self.coco_gt.loadCats(self.coco_gt.getCatIds())
        return {c["id"]: c.get("name", str(c["id"])) for c in cats}
    
    def cleanup(self):
        # reset pycocotools
        self.coco_eval = {}
        for iou_type in self.iou_types:
            self.coco_eval[iou_type] = COCOeval(self.coco_gt, iouType=iou_type)
        self.img_ids = []
        self.eval_imgs = {k: [] for k in self.iou_types}
        # reset torchmetrics buffers
        self._tm_bbox.reset()
        # if hasattr(self, "_tm_segm"): self._tm_segm.reset()
        self._tm_preds.clear()
        self._tm_tgts.clear()

    def update(self, predictions):
        """
        predictions: Dict[image_id -> {
            "boxes": FloatTensor[N,4] (xyxy), "scores": FloatTensor[N],
            "labels": LongTensor[N],  (optional) "masks": Bool/0-1 Tensor[N,H,W]
        }]
        """
        img_ids = list(np.unique(list(predictions.keys())))
        self.img_ids.extend(img_ids)

        # ---- standard COCO flow (unchanged)
        for iou_type in self.iou_types:
            results = self.prepare(predictions, iou_type)
            self.coco_gt.dataset.setdefault('info', {'description': 'generated by rt-detrv2'})
            with open(os.devnull, 'w') as devnull:
                with contextlib.redirect_stdout(devnull):
                    coco_dt = COCO.loadRes(self.coco_gt, results) if results else COCO()
            coco_eval = self.coco_eval[iou_type]
            coco_eval.cocoDt = coco_dt
            coco_eval.params.imgIds = list(img_ids)
            img_ids_, eval_imgs = evaluate(coco_eval)
            self.eval_imgs[iou_type].append(eval_imgs)

        # ---- TorchMetrics flow
        # Cache preds for these img_ids
        for img_id in img_ids:
            pred = predictions.get(img_id, None)
            if pred is None:
                # empty prediction
                self._tm_preds[img_id] = {
                    "boxes": torch.zeros((0,4), dtype=torch.float32),
                    "scores": torch.zeros((0,), dtype=torch.float32),
                    "labels": torch.zeros((0,), dtype=torch.long),
                }
            else:
                # ensure cpu tensors
                self._tm_preds[img_id] = {
                    "boxes": pred["boxes"].detach().to("cpu"),
                    "scores": pred["scores"].detach().to("cpu"),
                    "labels": pred["labels"].detach().to("cpu"),
                }
                if "masks" in pred:
                    # torchmetrics expects masks as [N,H,W] uint8/bool
                    m = pred["masks"]
                    if m.ndim == 4:  # [N,1,H,W] -> [N,H,W]
                        m = m[:,0]
                    self._tm_preds[img_id]["masks"] = (m.detach().to("cpu") > 0.5).to(torch.uint8)

        # Build targets from COCO GT for these img_ids and feed to torchmetrics
        targets = self._build_targets_from_coco(img_ids)
        preds_list   = []
        targets_list = []
        for img_id in img_ids:
            if img_id not in self._tm_preds:
                # make sure we have an empty pred
                self._tm_preds[img_id] = {
                    "boxes": torch.zeros((0,4), dtype=torch.float32),
                    "scores": torch.zeros((0,), dtype=torch.float32),
                    "labels": torch.zeros((0,), dtype=torch.long),
                }
            preds_list.append(self._tm_preds[img_id])
            targets_list.append(targets[img_id])
        # Update TM metrics
        if any(k in self.iou_types for k in ["bbox"]):
            self._tm_bbox.update(preds_list, targets_list)
        # if "segm" in self.iou_types and hasattr(self, "_tm_segm"):
        #     # torchmetrics segm requires 'masks' in preds/targets
        #     self._tm_segm.update(preds_list, targets_list)

    def _build_targets_from_coco(self, img_ids):
        """
        Returns dict[img_id -> target] where target is:
          {"boxes": FloatTensor[M,4] (xyxy), "labels": LongTensor[M], (optional) "masks": ByteTensor[M,H,W]}
        """
        out = {}
        for img_id in img_ids:
            ann_ids = self.coco_gt.getAnnIds(imgIds=[img_id])
            anns = self.coco_gt.loadAnns(ann_ids)
            # skip crowd anns for fair comparison (COCO eval also treats crowds specially)
            anns = [a for a in anns if a.get("iscrowd", 0) == 0]

            boxes = []
            labels = []
            masks = []
            for a in anns:
                # COCO GT boxes are [x, y, w, h] in absolute pixels
                x, y, w, h = a["bbox"]
                x2, y2 = x + w, y + h
                boxes.append([x, y, x2, y2])
                labels.append(int(a["category_id"]))
                if "segmentation" in a and isinstance(a["segmentation"], (list, dict)):
                    try:
                        rle = mask_util.frPyObjects(a["segmentation"], a["height"], a["width"]) \
                              if isinstance(a["segmentation"], list) else a["segmentation"]
                        m = mask_util.decode(rle)  # [H,W] or [H,W,K]
                        if m.ndim == 3:
                            m = (m.max(-1) > 0).astype(np.uint8)
                        masks.append(torch.from_numpy(m.astype(np.uint8)))
                    except Exception:
                        pass

            if len(boxes) == 0:
                target = {
                    "boxes": torch.zeros((0,4), dtype=torch.float32),
                    "labels": torch.zeros((0,), dtype=torch.long),
                }
            else:
                target = {
                    "boxes": torch.tensor(boxes, dtype=torch.float32),
                    "labels": torch.tensor(labels, dtype=torch.long),
                }
                if len(masks) == len(boxes) and len(masks) > 0:
                    target["masks"] = torch.stack(masks, dim=0)

            out[img_id] = target
            self._tm_tgts[img_id] = target
        return out

    def synchronize_between_processes(self):
        # ---- pycocotools merge (unchanged)
        for iou_type in self.iou_types:
            self.eval_imgs[iou_type] = np.concatenate(self.eval_imgs[iou_type], 2)
            create_common_coco_eval(self.coco_eval[iou_type], self.img_ids, self.eval_imgs[iou_type])

        # ---- TorchMetrics: no manual gather required if running under DDP;
        # MeanAveragePrecision handles synchronization during compute().
        # If you're not using DDP, nothing special is needed.

    def accumulate(self):
        # pycocotools
        for coco_eval in self.coco_eval.values():
            coco_eval.accumulate()
        # torchmetrics does accumulation incrementally via update(); nothing needed here.

    def summarize(self):
        # ---- pycocotools
        for iou_type, coco_eval in self.coco_eval.items():
            print("IoU metric (pycocotools): {}".format(iou_type))
            coco_eval.summarize()

        # ---- TorchMetrics output
        thr_str = ", ".join([f"{t:.2f}" for t in self._tm_iou_thresholds])
        print(f"TorchMetrics MeanAveragePrecision (bbox) @ IoU={thr_str}:")
        
        tm_bbox_res = self._tm_bbox.compute()
        tm_bbox_res = {k: (v.item() if isinstance(v, torch.Tensor) and v.numel() == 1 else v)
                    for k, v in tm_bbox_res.items()}

        # Global summary (these are already averaged over the specified IoUs; here it's just 0.4)
        for k in ["map", "mar_1","mar_10","mar_100"]:
            if k in tm_bbox_res and isinstance(tm_bbox_res[k], (float, int)):
                print(f"  {k:>9}: {tm_bbox_res[k]:.4f}")

        # Per-class table with explicit IoU notice
        if "map_per_class" in tm_bbox_res and "classes" in tm_bbox_res:
            id_to_name = self._coco_classes_lookup()

            # Normalize to list
            cls_ids = tm_bbox_res["classes"]
            aps = tm_bbox_res["map_per_class"]
            mps = tm_bbox_res["mar_100_per_class"]

            if torch.is_tensor(cls_ids):
                cls_ids = cls_ids.tolist()
            elif isinstance(cls_ids, (int, float)):
                cls_ids = [int(cls_ids)]
            elif not isinstance(cls_ids, (list, tuple)):
                cls_ids = list(cls_ids)

            if torch.is_tensor(aps):
                aps = aps.tolist()
            elif isinstance(aps, (float, int)):
                aps = [float(aps)]
            elif not isinstance(aps, (list, tuple)):
                aps = list(aps)
                
            if torch.is_tensor(mps):
                mps = mps.tolist()
            elif isinstance(mps, (float, int)):
                mps = [float(mps)]
            elif not isinstance(mps, (list, tuple)):
                mps = list(mps)

            print(f"\nPer-class AP (bbox) @ IoU={thr_str}:")
            print(f"{'cat_id':>8}  {'class':<20}  {'AP':>6}")
            for cid, ap in zip(cls_ids, aps):
                cname = id_to_name.get(int(cid), str(int(cid)))
                val = float(ap) if ap is not None else float("nan")
                print(f"{cid:>8}  {cname:<20}  {val:6.4f}")
                
            print(f"{'cat_id':>8}  {'class':<20}  {'AR':>6}")
            for cid, ap in zip(cls_ids, mps):
                cname = id_to_name.get(int(cid), str(int(cid)))
                val = float(ap) if ap is not None else float("nan")
                print(f"{cid:>8}  {cname:<20}  {val:6.4f}")

        # If you also track segm:
        # if hasattr(self, "_tm_segm"):
        #     print("\nTorchMetrics MeanAveragePrecision (segm):")
        #     tm_segm_res = self._tm_segm.compute()
        #     tm_segm_res = {k: (v.item() if isinstance(v, torch.Tensor) and v.numel() == 1 else v)
        #                    for k, v in tm_segm_res.items()}
        #     if "map" in tm_segm_res: print(f"  {'map':>9}: {tm_segm_res['map']:.4f}")
        #     if "map_per_class" in tm_segm_res and "classes" in tm_segm_res:
        #         id_to_name = self._coco_classes_lookup()
        #         cls_ids = tm_segm_res["classes"].tolist()
        #         aps = tm_segm_res["map_per_class"].tolist()
        #         print("\nPer-class AP (segm):")
        #         print(f"{'cat_id':>8}  {'class':<20}  {'AP':>6}")
        #         for cid, ap in zip(cls_ids, aps):
        #             cname = id_to_name.get(int(cid), str(int(cid)))
        #             val = float(ap) if ap is not None else float("nan")
        #             print(f"{cid:>8}  {cname:<20}  {val:6.4f}")


    # ------------------- prepare* methods for pycocotools (unchanged) -------------------

    def prepare(self, predictions, iou_type):
        if iou_type == "bbox":
            return self.prepare_for_coco_detection(predictions)
        elif iou_type == "segm":
            return self.prepare_for_coco_segmentation(predictions)
        elif iou_type == "keypoints":
            return self.prepare_for_coco_keypoint(predictions)
        else:
            raise ValueError("Unknown iou type {}".format(iou_type))

    def prepare_for_coco_detection(self, predictions):
        coco_results = []
        for original_id, prediction in predictions.items():
            if len(prediction) == 0:
                continue
            boxes = prediction["boxes"]
            boxes = convert_to_xywh(boxes).tolist()
            scores = prediction["scores"].tolist()
            labels = prediction["labels"].tolist()
            coco_results.extend(
                [
                    {
                        "image_id": original_id,
                        "category_id": labels[k],
                        "bbox": box,
                        "score": scores[k],
                    }
                    for k, box in enumerate(boxes)
                ]
            )
        return coco_results

    def prepare_for_coco_segmentation(self, predictions):
        coco_results = []
        for original_id, prediction in predictions.items():
            if len(prediction) == 0:
                continue

            masks = prediction["masks"] > 0.5
            scores = prediction["scores"].tolist()
            labels = prediction["labels"].tolist()

            rles = [
                mask_util.encode(np.array(mask.cpu()[0, :, :, np.newaxis], dtype=np.uint8, order="F"))[0]
                if mask.ndim == 4 else
                mask_util.encode(np.array(mask.cpu()[:, :, np.newaxis], dtype=np.uint8, order="F"))[0]
                for mask in masks
            ]
            for rle in rles:
                rle["counts"] = rle["counts"].decode("utf-8")

            coco_results.extend(
                [
                    {
                        "image_id": original_id,
                        "category_id": labels[k],
                        "segmentation": rle,
                        "score": scores[k],
                    }
                    for k, rle in enumerate(rles)
                ]
            )
        return coco_results

    def prepare_for_coco_keypoint(self, predictions):
        coco_results = []
        for original_id, prediction in predictions.items():
            if len(prediction) == 0:
                continue

            boxes = prediction["boxes"]
            boxes = convert_to_xywh(boxes).tolist()
            scores = prediction["scores"].tolist()
            labels = prediction["labels"].tolist()
            keypoints = prediction["keypoints"]
            keypoints = keypoints.flatten(start_dim=1).tolist()

            coco_results.extend(
                [
                    {
                        "image_id": original_id,
                        "category_id": labels[k],
                        'keypoints': keypoint,
                        "score": scores[k],
                    }
                    for k, keypoint in enumerate(keypoints)
                ]
            )
        return coco_results


def convert_to_xywh(boxes):
    xmin, ymin, xmax, ymax = boxes.unbind(1)
    return torch.stack((xmin, ymin, xmax - xmin, ymax - ymin), dim=1)


def merge(img_ids, eval_imgs):
    all_img_ids = dist_utils.all_gather(img_ids)
    all_eval_imgs = dist_utils.all_gather(eval_imgs)

    merged_img_ids = []
    for p in all_img_ids:
        merged_img_ids.extend(p)

    merged_eval_imgs = []
    for p in all_eval_imgs:
        merged_eval_imgs.append(p)

    merged_img_ids = np.array(merged_img_ids)
    merged_eval_imgs = np.concatenate(merged_eval_imgs, 2)

    # keep only unique (and in sorted order) images
    merged_img_ids, idx = np.unique(merged_img_ids, return_index=True)
    merged_eval_imgs = merged_eval_imgs[..., idx]

    return merged_img_ids, merged_eval_imgs


def create_common_coco_eval(coco_eval, img_ids, eval_imgs):
    img_ids, eval_imgs = merge(img_ids, eval_imgs)
    img_ids = list(img_ids)
    eval_imgs = list(eval_imgs.flatten())
    coco_eval.evalImgs = eval_imgs
    coco_eval.params.imgIds = img_ids
    coco_eval._paramsEval = copy.deepcopy(coco_eval.params)

#################################################################
# From pycocotools, just removed the prints and fixed a Python3 issue
#################################################################

def evaluate(self):
    """
    Run per image evaluation on given images and store results (a list of dict) in self.evalImgs
    :return: None
    """
    p = self.params
    if p.useSegm is not None:
        p.iouType = 'segm' if p.useSegm == 1 else 'bbox'
        print('useSegm (deprecated) is not None. Running {} evaluation'.format(p.iouType))
    p.imgIds = list(np.unique(p.imgIds))
    if p.useCats:
        p.catIds = list(np.unique(p.catIds))
    p.maxDets = sorted(p.maxDets)
    self.params = p

    self._prepare()
    catIds = p.catIds if p.useCats else [-1]

    if p.iouType == 'segm' or p.iouType == 'bbox':
        computeIoU = self.computeIoU
    elif p.iouType == 'keypoints':
        computeIoU = self.computeOks
    self.ious = {
        (imgId, catId): computeIoU(imgId, catId)
        for imgId in p.imgIds
        for catId in catIds}

    evaluateImg = self.evaluateImg
    maxDet = p.maxDets[-1]
    evalImgs = [
        evaluateImg(imgId, catId, areaRng, maxDet)
        for catId in catIds
        for areaRng in p.areaRng
        for imgId in p.imgIds
    ]
    evalImgs = np.asarray(evalImgs).reshape(len(catIds), len(p.areaRng), len(p.imgIds))
    self._paramsEval = copy.deepcopy(self.params)
    return p.imgIds, evalImgs
