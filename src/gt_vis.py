#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GT Visualizer (boxes from normalized JSON)
- Input root: e.g., /.../test/
- Auto-discovers:
    images under <root>/images/** (recursive)
    segmentations under <root>/segmentations/** (recursive)
- JSON format per file:
    {
      "<imagePath>": {
        "boxes": [[x0,y0,x1,y1], ...],
        "labels": [int, ...]
      }
    }
- Output: <root>/gt_vis/<relative_path_of_image>.jpg/png
"""

import argparse
import json
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from PIL import Image, ImageDraw

# --------------------- Class map (IDs and names) ---------------------
# IDs start at 1, matching your order:
ID_TO_NAME = {
    1: "hole",
    2: "strips",
    3: "stain",
    4: "line",
    5: "knots",
    6: "fiber",
    7: "band",
    8: "pilling",
    9: "colouring_fold",
}

# Distinct colors per class (RGB). Extend/change as needed.
ID_TO_COLOR = {
    1: (255, 0, 0),      # hole - red
    2: (0, 200, 255),    # strips - cyan-ish
    3: (255, 165, 0),    # stain - orange
    4: (255, 255, 0),    # line - yellow
    5: (138, 43, 226),   # knots - blueviolet
    6: (0, 255, 0),      # fiber - green
    7: (255, 0, 255),    # band - magenta
    8: (0, 0, 255),      # pilling - blue
    9: (139, 69, 19),    # colouring_fold - brown
}

IMG_EXTS = (".jpg", ".jpeg", ".png", ".bmp", ".JPG", ".JPEG", ".PNG", ".BMP")
JSON_EXTS = (".json",)


def index_images(images_root: Path) -> Dict[str, Path]:
    """Build a basename->fullpath index for images recursively."""
    index: Dict[str, Path] = {}
    for p in images_root.rglob("*"):
        if p.suffix in IMG_EXTS and p.is_file():
            index[p.name] = p
    return index


def discover_paths(root: Path) -> Tuple[Path, Path, Path]:
    """Return (images_root, segmentations_root, out_root)."""
    images_root = root / "images"
    seg_root = root / "segmentations"
    out_root = root / "gt_vis"
    if not images_root.exists():
        raise FileNotFoundError(f"Images root not found: {images_root}")
    if not seg_root.exists():
        raise FileNotFoundError(f"Segmentations root not found: {seg_root}")
    out_root.mkdir(parents=True, exist_ok=True)
    return images_root, seg_root, out_root


def load_json(json_path: Path) -> Dict:
    with json_path.open("r", encoding="utf-8") as f:
        return json.load(f)


def find_image_for_key(key: str, images_index: Dict[str, Path], images_root: Path) -> Optional[Path]:
    """
    key can be a filename ('xxx.jpg') or a relative/original path ('.../xxx.jpg').
    Strategy:
      1) If key is absolute/relative path and exists -> use it.
      2) Else, lookup by basename in prebuilt index.
    """
    kpath = Path(key)
    # 1) try as-is (absolute or relative to images_root)
    if kpath.is_file():
        return kpath
    candidate = images_root / kpath
    if candidate.is_file():
        return candidate
    # 2) try basename lookup
    base = kpath.name
    return images_index.get(base, None)


def draw_boxes(
    img: Image.Image,
    boxes: List[List[float]],
    labels: List[int],
    thickness: int = 3,
    alpha: float = 0.20,
    draw_text: bool = True,
) -> Image.Image:
    """Draw rectangles with per-class colors. Optional semi-transparent fill."""
    draw = ImageDraw.Draw(img)
    # For semi-transparent fill, create an overlay:
    overlay = Image.new("RGBA", img.size, (0, 0, 0, 0))
    ovr = ImageDraw.Draw(overlay)

    for b, lbl in zip(boxes, labels):
        x0, y0, x1, y1 = map(float, b)
        color = ID_TO_COLOR.get(lbl, (255, 255, 255))
        # outline on base image
        for t in range(thickness):  # simulate thickness
            draw.rectangle([x0 - t, y0 - t, x1 + t, y1 + t], outline=color)

        # semi-transparent fill on overlay
        fill_color = (*color, int(255 * alpha))
        ovr.rectangle([x0, y0, x1, y1], fill=fill_color)

        if draw_text:
            name = ID_TO_NAME.get(lbl, str(lbl))
            draw.text((x0 + 2, y0 + 2), name, fill=(255, 255, 255))

    # alpha composite
    result = Image.alpha_composite(img.convert("RGBA"), overlay).convert("RGB")
    return result


def process_json(
    json_path: Path,
    images_index: Dict[str, Path],
    images_root: Path,
    out_root: Path,
    keep_rel_under: Optional[Path] = None,
    thickness: int = 3,
    alpha: float = 0.20,
    draw_text: bool = True,
) -> None:
    data = load_json(json_path)

    # Expect a dict with 1..N entries (usually 1). Iterate robustly.
    for key, item in data.items():
        boxes = item.get("boxes", [])
        labels = item.get("labels", [])
        if not boxes or not labels or len(boxes) != len(labels):
            # Skip inconsistent entries
            continue

        img_path = find_image_for_key(key, images_index, images_root)
        if img_path is None or not img_path.is_file():
            # try also by basename if key itself is not the image filename
            img_path = images_index.get(Path(key).name)
        if img_path is None or not img_path.is_file():
            # could not resolve image
            continue

        img = Image.open(str(img_path)).convert("RGB")
        img_drawn = draw_boxes(img, boxes, labels, thickness=thickness, alpha=alpha, draw_text=draw_text)

        # Preserve relative structure under images_root if possible
        try:
            rel = img_path.relative_to(images_root)
        except ValueError:
            # Fallback: use just the filename
            rel = Path(img_path.name)

        out_path = out_root / rel
        out_path.parent.mkdir(parents=True, exist_ok=True)
        img_drawn.save(str(out_path))


def main():
    parser = argparse.ArgumentParser(description="Draw GT boxes from normalized JSON into images.")
    parser.add_argument("root", type=str,
                        help="Root folder (e.g., /mnt/.../test/). Will look under images/ and segmentations/ recursively.")
    parser.add_argument("--images", type=str, default=None,
                        help="Override images root (default: <root>/images)")
    parser.add_argument("--segmentations", type=str, default=None,
                        help="Override segmentations root (default: <root>/segmentations)")
    parser.add_argument("--out", type=str, default=None,
                        help="Output root (default: <root>/gt_vis)")
    parser.add_argument("--alpha", type=float, default=0.20, help="Fill transparency [0..1].")
    parser.add_argument("--thickness", type=int, default=3, help="Box outline thickness (approx).")
    parser.add_argument("--no-text", action="store_true", help="Do not draw class name text.")
    args = parser.parse_args()

    root = Path(args.root).resolve()
    images_root_default, seg_root_default, out_root_default = discover_paths(root)

    images_root = Path(args.images).resolve() if args.images else images_root_default
    seg_root = Path(args.segmentations).resolve() if args.segmentations else seg_root_default
    out_root = Path(args.out).resolve() if args.out else out_root_default

    images_index = index_images(images_root)

    json_files: List[Path] = []
    for ext in JSON_EXTS:
        json_files.extend(seg_root.rglob(f"*{ext}"))
    json_files = sorted(json_files)

    for jpath in json_files:
        process_json(
            jpath,
            images_index=images_index,
            images_root=images_root,
            out_root=out_root,
            thickness=args.thickness,
            alpha=args.alpha,
            draw_text=not args.no_text,
        )

    print(f"Done. Wrote visualizations under: {out_root}")


if __name__ == "__main__":
    main()

