# tools/confusion_matrix.py
import os
import sys
import argparse
from pathlib import Path
import numpy as np
import torch
import torch.nn as nn
from tqdm import tqdm
import matplotlib.pyplot as plt

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from src.misc import dist_utils
from src.core import YAMLConfig, yaml_utils
from src.solver import TASKS

# ---------- utilidades ----------
def box_iou_xyxy(a, b):
    # a: [Na,4], b: [Nb,4] -> xyxy
    if len(a) == 0 or len(b) == 0:
        return np.zeros((len(a), len(b)), dtype=np.float32)
    inter_x1 = np.maximum(a[:, None, 0], b[None, :, 0])
    inter_y1 = np.maximum(a[:, None, 1], b[None, :, 1])
    inter_x2 = np.minimum(a[:, None, 2], b[None, :, 2])
    inter_y2 = np.minimum(a[:, None, 3], b[None, :, 3])
    inter_w = np.clip(inter_x2 - inter_x1, 0, None)
    inter_h = np.clip(inter_y2 - inter_y1, 0, None)
    inter = inter_w * inter_h
    area_a = (a[:, 2] - a[:, 0]) * (a[:, 3] - a[:, 1])
    area_b = (b[:, 2] - b[:, 0]) * (b[:, 3] - b[:, 1])
    union = area_a[:, None] + area_b[None, :] - inter
    return inter / np.clip(union, 1e-9, None)

def greedy_match(pred_boxes, pred_cls, pred_scores, gt_boxes, gt_cls, iou_thr=0.5, score_thr=0.05):
    keep = pred_scores >= score_thr
    pred_boxes = pred_boxes[keep]
    pred_cls   = pred_cls[keep]
    pred_scores= pred_scores[keep]

    if len(pred_boxes) == 0 or len(gt_boxes) == 0:
        return [], set(range(len(pred_boxes))), set(range(len(gt_boxes)))

    iou = box_iou_xyxy(pred_boxes, gt_boxes)
    matched = []
    used_p, used_g = set(), set()
    order = np.argsort(-pred_scores)  # por score desc
    for p in order:
        g = int(np.argmax(iou[p]))
        if iou[p, g] >= iou_thr and (p not in used_p) and (g not in used_g):
            matched.append((p, g))
            used_p.add(p); used_g.add(g)
    unmatched_p = set(range(len(pred_boxes))) - used_p
    unmatched_g = set(range(len(gt_boxes))) - used_g
    return matched, unmatched_p, unmatched_g

def ensure_xyxy_from_cxcywh_norm(b, img_w, img_h):
    # b: [N,4] en cx,cy,w,h normalizado [0,1]
    cx, cy, w, h = b.T
    x1 = (cx - w/2) * img_w
    y1 = (cy - h/2) * img_h
    x2 = (cx + w/2) * img_w
    y2 = (cy + h/2) * img_h
    return np.stack([x1, y1, x2, y2], axis=1)

def plot_confmat(cm, class_names, out_png):
    fig, ax = plt.subplots(figsize=(max(6, 0.5*len(class_names)+2),
                                    max(5, 0.5*len(class_names)+2)))
    im = ax.imshow(cm, interpolation='nearest')
    ax.figure.colorbar(im, ax=ax)
    ax.set(xticks=np.arange(cm.shape[1]),
           yticks=np.arange(cm.shape[0]),
           xticklabels=class_names,
           yticklabels=class_names,
           ylabel='Predicho',
           xlabel='Real',
           title='Matriz de confusión (detección, deploy)')
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
    thresh = cm.max() * 0.6
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            val = int(cm[i, j])
            if val > 0:
                ax.text(j, i, str(val), ha="center", va="center",
                        color="white" if cm[i, j] > thresh else "black", fontsize=8)
    fig.tight_layout()
    fig.savefig(out_png, dpi=200)
    plt.close(fig)

# ---------- script principal ----------
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('-c', '--config', type=str, required=True)
    parser.add_argument('-r', '--resume', type=str, required=True)
    parser.add_argument('--device', type=str, default='cuda')
    # CAMBIO 1: IoU por defecto = 0.4 para alinear con tu YAML
    parser.add_argument('--iou-thr', type=float, default=0.4)
    parser.add_argument('--score-thr', type=float, default=0.05)
    parser.add_argument('--save-dir', type=str, default='.')
    parser.add_argument('--include-bg', action='store_true')
    parser.add_argument('--gt-box-fmt', type=str, default='xyxy',
                        choices=['xyxy', 'cxcywh_norm'])
    parser.add_argument('-u', '--update', nargs='+', help='overrides del yaml')
    args = parser.parse_args()
    
    cfg = YAMLConfig(args.config, resume=args.resume)


    # ... (setup distribuido y carga cfg / checkpoint idénticos)

    # Modelo deploy idéntico
    class DeployModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.model = cfg.model.deploy()
            self.postprocessor = cfg.postprocessor.deploy()
        def forward(self, images, orig_target_sizes):
            out = self.model(images)
            return self.postprocessor(out, orig_target_sizes)  # (labels, boxes, scores)

    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    model = DeployModel().to(device).eval()

    solver = TASKS[cfg.yaml_cfg['task']](cfg)
    val_loader = solver.val_loader

    # CAMBIO 2: obtener num_classes y class_names aunque no estén en dataset
    class_names = getattr(solver, 'class_names', None)
    if class_names is None:
        class_names = cfg.yaml_cfg.get('dataset', {}).get('class_names', None)
    if class_names is None:
        # Si no hay nombres, usa num_classes del YAML (tu archivo tiene 'num_classes: 9')
        num_classes = cfg.yaml_cfg.get('num_classes', None)
        if num_classes is None:
            raise RuntimeError("No se encontraron 'class_names' ni 'num_classes' en el config.")
        class_names = [str(i) for i in range(int(num_classes))]
    num_classes = len(class_names)

    bg = 1 if args.include_bg else 0
    cm = np.zeros((num_classes + bg, num_classes + bg), dtype=np.int64)

    # ---------- util para obtener claves robustas ----------
    def get_batch_images_targets(batch):
        # Aceptar 'image' o 'images'
        if 'image' in batch:   images = batch['image']
        elif 'images' in batch: images = batch['images']
        else: raise KeyError("Batch no contiene 'image' ni 'images'.")

        if 'target' in batch:   targets = batch['target']
        elif 'targets' in batch: targets = batch['targets']
        else: raise KeyError("Batch no contiene 'target' ni 'targets'.")
        return images, targets

    def get_orig_sizes(batch, images):
        # Busca varias posibilidades comunes
        cand = [
            'orig_size', 'orig_sizes',
            'orig_target_sizes', 'size', 'sizes',
            'rescaled_sizes', 'im_hw', 'hw', 'shape'
        ]
        for k in cand:
            if k in batch:
                t = batch[k]
                if not isinstance(t, torch.Tensor):
                    t = torch.as_tensor(t)
                t = t.to(device)
                # Normalizamos a [B,2] = [W,H]
                if t.ndim == 1:  # [2] -> [1,2]
                    t = t.unsqueeze(0)
                if t.shape[-1] == 2:
                    return t
                # A veces viene [H,W]
                if t.shape[-1] == 2 and 'hw' in k:
                    return torch.flip(t, dims=[-1])
        # Fallback: usa tamaño del tensor (no ideal si el loader hace resize sin guardar original)
        B, _, H, W = images.shape
        return torch.tensor([W, H], device=device).unsqueeze(0).repeat(B, 1)

    # ---------- bucle ----------
    with torch.no_grad():
        for batch in tqdm(val_loader, desc='Inferencia (deploy) para matriz de confusión'):
            images, targets = get_batch_images_targets(batch)
            images = images.to(device)
            orig_sizes = get_orig_sizes(batch, images)

            labels_list, boxes_list, scores_list = model(images, orig_sizes)

            for i in range(len(targets)):
                pl = labels_list[i].detach().cpu().numpy().astype(np.int64)
                pb = boxes_list[i].detach().cpu().numpy()
                ps = scores_list[i].detach().cpu().numpy()

                gt = targets[i]
                # Acepta claves 'labels'/'boxes' o 'classes'/'bboxes'
                gl = (gt['labels'] if 'labels' in gt else gt['classes']).detach().cpu().numpy().astype(np.int64)
                gb = (gt['boxes'] if 'boxes' in gt else gt['bboxes']).detach().cpu().numpy()

                if args.gt_box_fmt == 'cxcywh_norm':
                    w, h = orig_sizes[i].tolist()
                    gb = ensure_xyxy_from_cxcywh_norm(gb, w, h)

                matched, un_p, un_g = greedy_match(pb, pl, ps, gb, gl,
                                                   iou_thr=args.iou_thr,
                                                   score_thr=args.score_thr)

                for p_idx, g_idx in matched:
                    r = int(pl[p_idx]); c = int(gl[g_idx])
                    if 0 <= r < num_classes and 0 <= c < num_classes:
                        cm[r, c] += 1

                if args.include_bg:
                    for p_idx in un_p:
                        r = int(pl[p_idx]); c = num_classes
                        if 0 <= r < num_classes: cm[r, c] += 1
                    for g_idx in un_g:
                        r = num_classes; c = int(gl[g_idx])
                        if 0 <= c < num_classes: cm[r, c] += 1

    # Guardar CSV y PNG
    csv_path = os.path.join(args.save_dir, 'confmat.csv')
    png_path = os.path.join(args.save_dir, 'confmat.png')

    # Encabezados
    if args.include_bg:
        row_names = class_names + ['BG']
        col_names = class_names + ['BG']
    else:
        row_names = class_names
        col_names = class_names

    with open(csv_path, 'w') as f:
        f.write(',' + ','.join(col_names) + '\n')
        for i, rn in enumerate(row_names):
            f.write(rn + ',' + ','.join(str(int(x)) for x in cm[i]) + '\n')

    plot_confmat(cm, col_names, png_path)
    print(f'Guardado: {csv_path}')
    print(f'Guardado: {png_path}')

    dist_utils.cleanup()

if __name__ == '__main__':
    main()
