"""Copyright(c) 2023 lyuwenyu. All Rights Reserved.
"""
import os 
import sys 
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '../..'))
from pathlib import Path

import torch
import torch.nn as nn 
import torchvision.transforms as T

import numpy as np 
from PIL import Image, ImageDraw

from tqdm import tqdm

from src.core import YAMLConfig


def draw(images, labels, boxes, scores, output_file, thrh = 0.5):
    draw = ImageDraw.Draw(images)

    scr = scores
    lab = labels[scr > thrh]
    box = boxes[scr > thrh]
    scrs = scores[scr > thrh]

    for j,b in enumerate(box):
        draw.rectangle(list(b), outline='green',width=2)
        draw.text((b[0], b[1]), text=f"{lab[j].item()} {round(scrs[j].item(),2)}", fill='blue', )

        images.save(output_file)


def main(args, ):
    """main
    """
    cfg = YAMLConfig(args.config, resume=args.resume)

    if args.resume:
        checkpoint = torch.load(args.resume, map_location='cpu') 
        if 'ema' in checkpoint:
            state = checkpoint['ema']['module']
        else:
            state = checkpoint['model']
    else:
        raise AttributeError('Only support resume to load model.state_dict by now.')

    # NOTE load train mode state -> convert to deploy mode
    cfg.model.load_state_dict(state)
    
    os.makedirs(args.out_folder, exist_ok=True)

    class Model(nn.Module):
        def __init__(self, ) -> None:
            super().__init__()
            self.model = cfg.model.deploy()
            self.postprocessor = cfg.postprocessor.deploy()
            
        def forward(self, images, orig_target_sizes):
            outputs = self.model(images)
            outputs = self.postprocessor(outputs, orig_target_sizes)
            return outputs

    model = Model().to(args.device)
    
    im_folder = list(Path(args.im_folder).glob('*.jpg')) + list(Path(args.im_folder).glob('*.png'))
    
    for im_file in tqdm(im_folder):
        im_pil = Image.open(str(im_file)).convert('RGB')
        w, h = im_pil.size
        orig_size = torch.tensor([w, h])[None].to(args.device)

        transforms = T.Compose([
            T.Resize((1024, 1024)),
            T.ToTensor(),
        ])
        im_data = transforms(im_pil)[None].to(args.device)

        output = model(im_data, orig_size)
        labels, boxes, scores = output
        

        output_file = os.path.join(args.out_folder, im_file.name)
        draw(im_pil, labels[0], boxes[0], scores[0], output_file, thrh=args.threshold)


if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('-c', '--config', type=str, )
    parser.add_argument('-r', '--resume', type=str, )
    parser.add_argument('-f', '--im-folder', type=str, )
    parser.add_argument('-o', '--out-folder', type=str, )
    parser.add_argument('-t', '--threshold', type=float, )
    parser.add_argument('-d', '--device', type=str, default='cpu')
    args = parser.parse_args()
    main(args)
