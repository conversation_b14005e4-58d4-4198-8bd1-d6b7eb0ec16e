#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Evaluate RT-DETRv2 on a folder AND produce filtered per-class views + full metrics.

Now aligned to COCO criteria:
- One-to-one greedy matching.
- IoU thresholds [.50:.95].
- No score threshold for AP/AR metrics.
- maxDets=100 per image (top-100 predictions by score).

Visuals and per-class filtered views remain, but use IoU >= iou_thr and the
prediction set already limited to top-100 per image.

Outputs unchanged in structure.
"""
import os
import sys
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Set, DefaultDict
from collections import defaultdict, Counter
import json

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '../..'))

import torch
import torch.nn as nn
import torchvision.transforms as T
from PIL import Image, ImageDraw, ImageFont
from tqdm import tqdm

from src.core import YAMLConfig
from src.data import CocoEvaluator
from pycocotools.coco import COCO

# ========= Class map =========
ID2NAME: Dict[int, str] = {
    1: "hole",
    2: "strips",
    3: "stain",
    4: "line",
    5: "knots",
    6: "fiber",
    7: "band",
    8: "platine",
    9: "band_new",
}
CLASSES: List[str] = [ID2NAME[i] for i in sorted(ID2NAME.keys())]
NAME2ID = {v: k for k, v in ID2NAME.items()}


def label_to_name(x: int) -> str:
    return ID2NAME.get(int(x), str(int(x)))


# ========= Geometry =========
def rect_intersection(a, b):
    ax0, ay0, ax1, ay1 = a
    bx0, by0, bx1, by1 = b
    x0, y0 = max(ax0, bx0), max(ay0, by0)
    x1, y1 = min(ax1, bx1), min(ay1, by1)
    if x1 > x0 and y1 > y0:
        return (x0, y0, x1, y1)
    return None


def rect_area(r):
    x0, y0, x1, y1 = r
    return max(0.0, x1 - x0) * max(0.0, y1 - y0)


def iou_rect(a, b):
    inter = rect_intersection(a, b)
    if inter is None:
        return 0.0
    ai = rect_area(inter)
    ua = rect_area(a) + rect_area(b) - ai
    return 0.0 if ua <= 0 else ai / ua


# ========= Fonts / text =========
def load_font(size: int):
    try:
        return ImageFont.truetype("DejaVuSans.ttf", size=size)
    except Exception:
        return ImageFont.load_default()


def _text_size(draw_ctx, text, font):
    try:
        l, t, r, b = draw_ctx.textbbox((0, 0), text, font=font, stroke_width=2)
        return (r - l, b - t)
    except Exception:
        return draw_ctx.textsize(text, font=font)


def _place_gt_label(box, W, H, tw, th, pad=4):
    x0, y0, x1, y1 = map(int, map(round, box))
    tx = max(0, x0 - tw - pad)
    ty = max(0, y0 - th - pad)
    return tx, ty


def _place_pred_label(box, W, H, tw, th, pad=4):
    x0, y0, x1, y1 = map(int, map(round, box))
    tx = min(W - tw - 1, x1 + pad)
    ty = max(0, y0 - th - pad)
    return tx, ty


def _draw_text_with_bg(draw_ctx, text, xy, font, fill=(255, 255, 255), bg=(0, 0, 0, 120)):
    x, y = xy
    tw, th = _text_size(draw_ctx, text, font)
    draw_ctx.rectangle([x - 2, y - 2, x + tw + 2, y + th + 2], fill=bg)
    draw_ctx.text((x, y), text=text, fill=fill, font=font, stroke_width=2, stroke_fill=(0, 0, 0))


# ========= Drawing =========
def _draw_rect_outline(draw_ctx, box, color, thickness=2):
    x0, y0, x1, y1 = map(float, box)
    for t in range(thickness):
        draw_ctx.rectangle([x0 - t, y0 - t, x1 + t, y1 + t], outline=color)


def draw_pred_labels(img, labels, boxes, scores, thrh, font):
    """Pred boxes + label (white) to RIGHT-top outside."""
    present: Set[str] = set()
    draw_ctx = ImageDraw.Draw(img, "RGBA")
    m = scores > thrh
    lab = labels[m]
    box = boxes[m]
    scrs = scores[m]
    W, H = img.size
    for j, b in enumerate(box):
        b = b.tolist()
        name = label_to_name(int(lab[j].item()))
        present.add(name)
        _draw_rect_outline(draw_ctx, b, (0, 255, 0), 2)
        txt = f"{name} {round(scrs[j].item(), 2)}"
        tw, th = _text_size(draw_ctx, txt, font)
        tx, ty = _place_pred_label(b, W, H, tw, th, pad=6)
        _draw_text_with_bg(draw_ctx, txt, (tx, ty), font, fill=(255, 255, 255))
    return present

# [COCO-ALIGN] Changed: use IoU >= iou_thr for overlaps; visuals inherit maxDets via prior truncation.
def paint_gt_and_overlaps(img, gt_boxes, gt_labels, pred_boxes, pred_labels, pred_scores,
                          score_thr, iou_thr, font):
    """
    Visual rules (COCO-aligned IoU threshold):
      - Overlap considered only if IoU >= iou_thr.
      - If pred class == GT class -> GREEN (TP).
      - If pred class != GT class -> BLUE (confused).
      - Pure FP (no GT with IoU >= iou_thr) -> PINK.
    """
    base_rgba = img.convert("RGBA")
    red_layer   = Image.new("RGBA", img.size, (0, 0, 0, 0))
    green_layer = Image.new("RGBA", img.size, (0, 0, 0, 0))
    blue_layer  = Image.new("RGBA", img.size, (0, 0, 0, 0))
    pink_layer  = Image.new("RGBA", img.size, (0, 0, 0, 0))

    red_draw   = ImageDraw.Draw(red_layer)
    green_draw = ImageDraw.Draw(green_layer)
    blue_draw  = ImageDraw.Draw(blue_layer)
    pink_draw  = ImageDraw.Draw(pink_layer)
    base_draw  = ImageDraw.Draw(base_rgba, "RGBA")

    pmask    = pred_scores > score_thr
    p_boxes  = pred_boxes[pmask]
    p_labels = pred_labels[pmask]
    p_scores = pred_scores[pmask]

    W, H = img.size
    per_class_status: DefaultDict[str, Set[str]] = defaultdict(set)

    # ---- GT + overlaps (TP or confused) ----
    for g_box, g_lbl in zip(gt_boxes, gt_labels):
        g_box  = list(map(float, g_box))
        g_name = label_to_name(int(g_lbl))

        # draw GT
        _draw_rect_outline(base_draw, g_box, (255, 255, 255), 3)
        tw, th = _text_size(base_draw, g_name, font)
        tx, ty = _place_gt_label(g_box, W, H, tw, th, pad=6)
        _draw_text_with_bg(base_draw, g_name, (tx, ty), font, fill=(255, 255, 255))

        x0, y0, x1, y1 = g_box
        red_draw.rectangle([x0, y0, x1, y1], fill=(255, 0, 0, int(255 * 0.25)))

        any_overlap = False
        any_match   = False
        any_conf    = False

        for pb, pl, ps in zip(p_boxes, p_labels, p_scores):
            p_box = list(map(float, pb.tolist()))
            iou = iou_rect(g_box, p_box)
            if iou < iou_thr:
                continue

            any_overlap = True
            inter = rect_intersection(g_box, p_box)
            ix0, iy0, ix1, iy1 = inter
            inter_box = [ix0, iy0, ix1, iy1]
            score_txt = f"{float(ps.item()):.2f}"

            if int(pl.item()) == int(g_lbl):
                any_match = True
                green_draw.rectangle(inter_box, fill=(0, 255, 0, int(255 * 0.25)))
                txt = f"p={score_txt}"
            else:
                any_conf = True
                blue_draw.rectangle(inter_box, fill=(0, 128, 255, int(255 * 0.25)))
                p_name = label_to_name(int(pl.item()))
                txt = f"conf→ {p_name} {score_txt}"

            ttw, tth = _text_size(base_draw, txt, font)
            tx2, ty2 = _place_pred_label(inter_box, W, H, ttw, tth, pad=4)
            _draw_text_with_bg(base_draw, txt, (tx2, ty2), font, fill=(255, 255, 255))

        if any_match:
            per_class_status[g_name].add("found")
        if any_conf:
            per_class_status[g_name].add("confused")
        if not any_overlap:
            per_class_status[g_name].add("Not_found")

    # ---- Pure FP (predictions with NO GT at IoU >= iou_thr) ----
    gt_all = [list(map(float, b)) for b in (gt_boxes or [])]
    for pb, pl, ps in zip(p_boxes, p_labels, p_scores):
        p_box = list(map(float, pb.tolist()))
        score_val = float(ps.item())
        overlaps_any = any(iou_rect(p_box, g) >= iou_thr for g in gt_all)
        if overlaps_any:
            continue  # not a pure FP

        # draw FP in pink
        pink_draw.rectangle(p_box, fill=(255, 105, 180, int(255 * 0.22)))
        _draw_rect_outline(pink_draw, p_box, (255, 20, 147), thickness=3)
        p_name = label_to_name(int(pl.item()))
        txt = f"FP {p_name} {round(score_val, 2)}"
        ttw, tth = _text_size(base_draw, txt, font)
        tx2, ty2 = _place_pred_label(p_box, W, H, ttw, tth, pad=6)
        _draw_text_with_bg(base_draw, txt, (tx2, ty2), font, fill=(255, 255, 255))

    out = Image.alpha_composite(base_rgba, red_layer)
    out = Image.alpha_composite(out, green_layer)
    out = Image.alpha_composite(out, blue_layer)
    out = Image.alpha_composite(out, pink_layer).convert("RGB")
    img.paste(out)
    return per_class_status


# ========= IO helpers =========
def _resolve_eval_size(args, cfg):
    if getattr(args, 'eval_size', None):
        h, w = args.eval_size
        return int(h), int(w)
    dataset_obj = getattr(cfg, 'dataset', None)
    eval_sz = getattr(dataset_obj, 'eval_spatial_size', None) if dataset_obj is not None else None
    if isinstance(eval_sz, (list, tuple)) and len(eval_sz) == 2:
        return int(eval_sz[0]), int(eval_sz[1])
    return 1024, 1024


def _collect_images(im_folder):
    p = Path(im_folder)
    pats = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.JPG', '*.JPEG', '*.PNG', '*.BMP']
    files = []
    for pat in pats:
        files += list(p.glob(pat))
    if not files:
        for pat in pats:
            files += list(p.rglob(pat))
    return sorted(set(files))


def _infer_seg_folder_from_images(im_folder):
    p = Path(im_folder)
    parts = list(p.parts)
    try:
        idx = parts.index('images')
        parts[idx] = 'segmentations'
        cand = Path(*parts)
        if cand.exists():
            return cand
    except ValueError:
        pass
    return None


def _load_json(path: Path):
    try:
        with path.open('r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return None


def _find_gt_json_for_image(img_path: Path, seg_folder: Path):
    stem = img_path.stem
    cand = list(seg_folder.rglob(f"{stem}.json"))
    if cand:
        return cand[0]
    for j in seg_folder.rglob("*.json"):
        data = _load_json(j)
        if isinstance(data, dict) and img_path.name in data.keys():
            return j
    return None


# ========= Pred label base helpers =========
def _infer_pred_base(pred_labels_tensor, gt_labels_list) -> int:
    try:
        p = pred_labels_tensor.detach().cpu().numpy().astype(int)
        vals = p.tolist()
    except Exception:
        vals = [int(x) for x in pred_labels_tensor]
    uniq_p = set(int(x) for x in vals)
    uniq_g = set(int(x) for x in (gt_labels_list or []))
    gt_looks_1_based = (len(uniq_g) == 0) or (all(1 <= x <= 999 for x in uniq_g) and (0 not in uniq_g))
    if 0 in uniq_p and 9 not in uniq_p and gt_looks_1_based:
        return 0
    return 1

def _normalize_pred_labels(pred_labels_tensor, base_choice: str, gt_labels_list):
    """Return labels normalized to 1..* if needed."""
    if base_choice == '1':
        return pred_labels_tensor
    if base_choice == '0':
        return pred_labels_tensor + 1
    inferred = _infer_pred_base(pred_labels_tensor, gt_labels_list)
    return pred_labels_tensor if inferred == 1 else (pred_labels_tensor + 1)


# ========= Save helpers =========
def write_csv(path, rows, header=None):
    path.parent.mkdir(parents=True, exist_ok=True)
    with path.open('w', encoding='utf-8') as f:
        if header:
            f.write(','.join(header) + '\n')
        for r in rows:
            f.write(','.join(map(str, r)) + '\n')


# ========= Filtered view per class =========
def render_filtered_view(orig_img: Image.Image,
                         class_name: str,
                         gt_boxes: List[List[float]], gt_labels: List[int],
                         pred_boxes: torch.Tensor, pred_labels: torch.Tensor, pred_scores: torch.Tensor,
                         score_thr: float, iou_thr: float, font):
    """Return a new image with ONLY this class painted (GT red + green/blue overlaps)."""
    img = orig_img.copy()

    # Class-specific GT
    gt_bx_cls, gt_lb_cls = [], []
    for b, l in zip(gt_boxes, gt_labels):
        if label_to_name(int(l)) == class_name:
            gt_bx_cls.append(b)
            gt_lb_cls.append(l)

    pmask = pred_scores > score_thr
    boxes = pred_boxes[pmask]
    plabels = pred_labels[pmask]
    pscores = pred_scores[pmask]

    _ = paint_gt_and_overlaps(img, gt_bx_cls, gt_lb_cls, boxes, plabels, pscores,
                              score_thr, iou_thr, font)

    if len(boxes) > 0:
        cls_id = NAME2ID.get(class_name, None)
        if cls_id is not None:
            mcls = (plabels.detach().cpu().numpy() == cls_id)
            if mcls.any():
                draw_ctx = ImageDraw.Draw(img, "RGBA")
                W, H = img.size
                for b, s in zip(boxes[mcls], pscores[mcls]):
                    b = b.tolist()
                    txt = f"{class_name} {round(float(s.item()), 2)}"
                    tw, th = _text_size(draw_ctx, txt, font)
                    tx, ty = _place_pred_label(b, W, H, tw, th, pad=6)
                    _draw_text_with_bg(draw_ctx, txt, (tx, ty), font, fill=(255, 255, 255))
    return img


# ========= Wrong prediction helpers =========
def _greedy_match_preds_to_gts_same_class(pred_boxes, pred_scores, gt_boxes, iou_thr: float):
    """Greedy one-to-one match within the SAME class (COCO-like), by score desc."""
    if len(pred_boxes) == 0:
        return set()
    if len(gt_boxes) == 0:
        return set(range(len(pred_boxes)))  # all wrong if no GT of this class

    # Sort predictions by score desc
    def _to_float(x):
        try:
            return float(x.item())
        except Exception:
            return float(x)

    order = sorted(range(len(pred_boxes)), key=lambda i: _to_float(pred_scores[i]), reverse=True)
    gt_used = set()
    unmatched = set()

    for i in order:
        pbox = pred_boxes[i]
        best_j, best_iou = -1, 0.0
        for j, gbox in enumerate(gt_boxes):
            if j in gt_used:
                continue
            iou = iou_rect(list(map(float, pbox.tolist() if hasattr(pbox, 'tolist') else pbox)),
                           list(map(float, gbox)))
            if iou > best_iou:
                best_iou, best_j = iou, j
        if best_j >= 0 and best_iou >= iou_thr:
            gt_used.add(best_j)
        else:
            unmatched.add(i)
    return unmatched


# ========= Metrics core (COCO-like, one-to-one, maxDets=100) =========
def match_image_one_class(preds, gts, iou_thr):
    """preds: list of (score, box, img_id, pidx); gts: list of (box, img_id) for the SAME class.
       One-to-one greedy by score, IoU ≥ iou_thr.
    """
    gt_used = defaultdict(set)  # img_id -> set of GT already matched
    preds_sorted = sorted(preds, key=lambda x: x[0], reverse=True)
    tp, fp, matches = [], [], []
    for score, pbox, img_id, pidx in preds_sorted:
        best_iou, best_j = 0.0, -1
        gts_img = [(j, gbox) for j, (gbox, iid) in enumerate(gts) if iid == img_id and j not in gt_used[img_id]]
        for j, gbox in gts_img:
            iou = iou_rect(pbox, gbox)
            if iou > best_iou:
                best_iou, best_j = iou, j
        if best_iou >= iou_thr and best_j >= 0:
            gt_used[img_id].add(best_j)
            tp.append(1); fp.append(0)
            matches.append((pidx, best_j, best_iou, img_id))
        else:
            tp.append(0); fp.append(1)
            matches.append((pidx, None, best_iou, img_id))
    return preds_sorted, tp, fp, matches


def precision_recall_ap(tp, fp, npos):
    """Compute precision-recall and AP (101-pt)."""
    if len(tp) == 0:
        return [0.0], [0.0], 0.0
    tp_cum = torch.cumsum(torch.tensor(tp), 0).tolist()
    fp_cum = torch.cumsum(torch.tensor(fp), 0).tolist()
    prec, rec = [], []
    for t, f in zip(tp_cum, fp_cum):
        p = t / max(1, (t + f))
        r = t / max(1, npos)
        prec.append(p); rec.append(r)
    # monotonic precision envelope
    mpre = prec[:]
    for i in range(len(mpre) - 2, -1, -1):
        mpre[i] = max(mpre[i], mpre[i + 1])
    ap = 0.0
    for k in range(101):
        rk = k / 100.0
        p = max([mp for mp, r in zip(mpre, rec) if r >= rk] or [0.0])
        ap += p / 101.0
    return rec, mpre, ap


# [COCO-ALIGN] enforce maxDets=100 for AP too, and no score threshold.
def evaluate_map_mar(gt_store, pred_store, class_names, iou_thresholds, mar_k=100, max_dets_ap=100):
    """COCO-like across IoU thresholds (all areas):
       - preds limited to top-100 per image (applied upstream in pred_store).
       - one-to-one matching.
       - AP over the full score range (no fixed threshold).
    """
    results = {
        'AP@0.50': {}, 'AP@[.50:.95]': {}, 'AR@0.50@K': {}, 'AR@[.50:.95]@K': {}
    }
    npos_by_cls = {c: 0 for c in class_names}
    for img_id, gts in gt_store.items():
        for g in gts:
            npos_by_cls[label_to_name(g['label'])] += 1

    aps_50, ars_50, aps_mean, ars_mean = [], [], [], []
    for cls in class_names:
        gts_cls = []
        for img_id, gts in gt_store.items():
            for g in gts:
                if label_to_name(g['label']) == cls:
                    gts_cls.append((g['box'], img_id))

        # preds already top-100 per image in pred_store; still build list
        preds_cls = []
        for img_id, preds in pred_store.items():
            # limit to top-100 per image for AP as well
            preds_img = preds[:max_dets_ap]  # pred_store already truncated; this is defensive
            for pi, p in enumerate(preds_img):
                preds_cls.append((p['score'], p['box'], img_id, (img_id, pi)))
        preds_cls_sorted = sorted(preds_cls, key=lambda x: x[0], reverse=True)

        ap_list, ar_list = [], []
        for thr in iou_thresholds:
            # only predictions of this class (COCO matches by category)
            preds_cls_only = [(s, b, im, idx) for (s, b, im, idx) in preds_cls_sorted
                              if label_to_name(pred_store[im][idx[1]]['label']) == cls]
            preds_sorted, tp, fp, _ = match_image_one_class(preds_cls_only, gts_cls, thr)
            rec, prec, ap = precision_recall_ap(tp, fp, npos_by_cls[cls])
            ap_list.append(ap)

            # AR@K≈100: at IoU thr, take up to K preds per image and compute recall
            kept_by_img = defaultdict(list)
            for s, b, im, idx in preds_cls_only:
                if len(kept_by_img[im]) < mar_k:
                    kept_by_img[im].append((s, b, im, idx))
            preds_k = []
            for im, v in kept_by_img.items():
                preds_k.extend(v)
            _, tp_k, fp_k, _ = match_image_one_class(preds_k, gts_cls, thr)
            tp_total = sum(tp_k)
            ar = tp_total / max(1, npos_by_cls[cls])
            ar_list.append(ar)

        AP50 = ap_list[0] if len(ap_list) > 0 else 0.0
        APm = sum(ap_list) / len(ap_list) if ap_list else 0.0
        AR50 = ar_list[0] if len(ar_list) > 0 else 0.0
        ARm = sum(ar_list) / len(ar_list) if ar_list else 0.0

        results['AP@0.50'][cls] = AP50
        results['AP@[.50:.95]'][cls] = APm
        results['AR@0.50@K'][cls] = AR50
        results['AR@[.50:.95]@K'][cls] = ARm

        aps_50.append(AP50); aps_mean.append(APm); ars_50.append(AR50); ars_mean.append(ARm)

    results['AP@0.50']['_global'] = sum(aps_50) / len(aps_50) if aps_50 else 0.0
    results['AP@[.50:.95]']['_global'] = sum(aps_mean) / len(aps_mean) if aps_mean else 0.0
    results['AR@0.50@K']['_global'] = sum(ars_50) / len(ars_50) if ars_50 else 0.0
    results['AR@[.50:.95]@K']['_global'] = sum(ars_mean) / len(ars_mean) if ars_mean else 0.0
    return results


# ========= Confusion & per-image TP-rate etc. (COCO-like one-to-one) =========
# [COCO-ALIGN] one-to-one, IoU ≥ iou_thr, no fixed score threshold (use all preds, limited by top-100).
def match_for_analysis(gt_store, pred_store, iou_thr=0.5):
    """
    COCO-like diagnostic matching:
      - One-to-one assignment at IoU ≥ iou_thr.
      - Greedy by IoU (per GT pick best unused pred; common proxy).
      - If matched pred class == GT class -> TP; else 'confused'.
      - FN: GTs with no matched pred.
      - FP: predictions unused after GT assignment.
    """
    confusion = Counter()
    tp_by_img_class = defaultdict(lambda: defaultdict(int))
    fn_by_class = Counter()
    fp_by_class = Counter()
    tp_iou_list_by_class = defaultdict(list)
    images_with_gt_by_class = Counter()
    images_with_tp_by_class = Counter()

    for img_id in gt_store.keys():
        gts = gt_store[img_id]
        preds = pred_store.get(img_id, [])  # already top-100

        present_classes = set(label_to_name(g['label']) for g in gts)
        for c in present_classes:
            images_with_gt_by_class[c] += 1

        # prepare structures
        used_pred = set()

        # ---- GT-driven assignment (one pred per GT; preds cannot be reused) ----
        for gi, g in enumerate(gts):
            gbox = g['box']
            gcls = label_to_name(g['label'])

            # pick best IoU among unused preds
            best_iou = 0.0
            best_j = -1
            for j, p in enumerate(preds):
                if j in used_pred:
                    continue
                iou = iou_rect(gbox, p['box'])
                if iou > best_iou:
                    best_iou = iou
                    best_j = j

            if best_j >= 0 and best_iou >= iou_thr:
                used_pred.add(best_j)
                pcls = label_to_name(preds[best_j]['label'])
                if pcls == gcls:
                    confusion[(gcls, gcls)] += 1
                    tp_by_img_class[img_id][gcls] += 1
                    tp_iou_list_by_class[gcls].append(best_iou)
                else:
                    confusion[(gcls, pcls)] += 1
            else:
                confusion[(gcls, '_missed')] += 1
                fn_by_class[gcls] += 1

        # ---- FP: unused predictions ----
        for j, p in enumerate(preds):
            if j not in used_pred:
                pcls = label_to_name(p['label'])
                confusion[('_extra', pcls)] += 1
                fp_by_class[pcls] += 1

        # images with >=1 TP per class
        for c, n in tp_by_img_class[img_id].items():
            if n > 0:
                images_with_tp_by_class[c] += 1

    return {
        'confusion': confusion,
        'tp_by_img_class': tp_by_img_class,
        'images_with_gt_by_class': images_with_gt_by_class,
        'images_with_tp_by_class': images_with_tp_by_class,
        'fn_by_class': fn_by_class,
        'fp_by_class': fp_by_class,
        'tp_iou_list_by_class': tp_iou_list_by_class,
    }


# ========= PR curves & plots (optional) =========
def safe_plot_pr(out_dir, pr_data):
    try:
        import matplotlib.pyplot as plt
        out_dir.mkdir(parents=True, exist_ok=True)
        for cls, d in pr_data.items():
            rec = d['rec']
            prec = d['prec']
            plt.figure()
            plt.plot(rec, prec)
            plt.xlabel('Recall')
            plt.ylabel('Precision')
            plt.title(f'PR - {cls}')
            plt.grid(True, alpha=0.3)
            plt.savefig(out_dir / f'pr_{cls}.png', dpi=150, bbox_inches='tight')
            plt.close()
    except Exception as e:
        print(f"[WARN] Plot PR failed: {e}")

def safe_plot_confusion(out_dir, confusion, classes, n_gts_by_class):
    """
    Extended confusion as antes:
      - confusion_matrix_counts.png
      - confusion_matrix_norm_row.png
    """
    try:
        import numpy as np
        import matplotlib.pyplot as plt

        out_dir.mkdir(parents=True, exist_ok=True)

        row_labels = list(classes)
        col_labels = list(classes) + ['no_detected', 'FP(no_gt)']
        row_labels_annot = [f"{c} (N={int(n_gts_by_class.get(c, 0))})" for c in row_labels]

        R = len(row_labels)
        C = len(col_labels)
        M = np.zeros((R, C), dtype=float)

        for (gt, pd), v in confusion.items():
            if gt in classes and pd in classes:
                i = row_labels.index(gt); j = col_labels.index(pd)
                M[i, j] += v
        for (gt, pd), v in confusion.items():
            if gt in classes and pd == '_missed':
                i = row_labels.index(gt); j = col_labels.index('no_detected')
                M[i, j] += v
        for (gt, pd), v in confusion.items():
            if gt == '_extra' and pd in classes:
                i = row_labels.index(pd); j = col_labels.index('FP(no_gt)')
                M[i, j] += v

        def _plot_counts(mat, title, out_png):
            import matplotlib.pyplot as plt
            plt.figure(figsize=(1 + 0.6 * C, 1 + 0.6 * R))
            im = plt.imshow(mat, interpolation='nearest', cmap='Blues')
            plt.xticks(range(C), col_labels, rotation=45, ha='right')
            plt.yticks(range(R), row_labels_annot)
            for i in range(R):
                for j in range(C):
                    val = mat[i, j]
                    if val > 0:
                        plt.text(j, i, f"{int(round(val))}", ha='center', va='center', fontsize=8)
            plt.title(title)
            plt.colorbar(im, fraction=0.046, pad=0.04)
            plt.tight_layout()
            plt.savefig(out_dir / out_png, dpi=150)
            plt.close()

        def _plot_row_norm(mat_counts, title, out_png):
            import matplotlib.pyplot as plt
            j_fp = col_labels.index('FP(no_gt)')
            Mnorm = np.zeros_like(mat_counts, dtype=float)
            for i, cls in enumerate(row_labels):
                denom = float(max(1, int(n_gts_by_class.get(cls, 0))))
                for j in range(C):
                    if j == j_fp:
                        Mnorm[i, j] = 0.0
                    else:
                        Mnorm[i, j] = mat_counts[i, j] / denom
            plt.figure(figsize=(1 + 0.6 * C, 1 + 0.6 * R))
            im = plt.imshow(Mnorm, interpolation='nearest', cmap='Blues', vmin=0.0, vmax=1.0)
            plt.xticks(range(C), col_labels, rotation=45, ha='right')
            plt.yticks(range(R), row_labels_annot)
            for i in range(R):
                for j in range(C):
                    val = Mnorm[i, j]
                    if j == j_fp:
                        cnt = mat_counts[i, j]
                        if cnt > 0:
                            plt.text(j, i, f"{int(round(cnt))}", ha='center', va='center', fontsize=8)
                    else:
                        pct = 100.0 * val
                        if pct > 0:
                            plt.text(j, i, f"{pct:.1f}%", ha='center', va='center', fontsize=8)
            plt.title(title + " matrix")
            cbar = plt.colorbar(im, fraction=0.046, pad=0.04)
            cbar.set_label('Percent of GT for that row class')
            plt.tight_layout()
            plt.savefig(out_dir / out_png, dpi=150)
            plt.close()

        _plot_counts(M, 'Confusion (counts)', 'confusion_matrix_counts.png')
        _plot_row_norm(M, 'Confusion (row-norm)', 'confusion_matrix_norm_row.png')

    except Exception as e:
        print(f"[WARN] Plot confusion (extended) failed: {e}")


def safe_hist_calibration(out_dir, scores_tp, scores_fp):
    try:
        import matplotlib.pyplot as plt
        out_dir.mkdir(parents=True, exist_ok=True)
        plt.figure()
        plt.hist(scores_tp, bins=20, alpha=0.6, label='TP')
        plt.hist(scores_fp, bins=20, alpha=0.6, label='FP')
        plt.xlabel('Score')
        plt.ylabel('Count')
        plt.legend()
        plt.title('Calibration TP vs FP')
        plt.savefig(out_dir / 'calibration_hist.png', dpi=150, bbox_inches='tight')
        plt.close()
    except Exception as e:
        print(f"[WARN] Calibration plot failed: {e}")


# ========= COCO GT Creation =========
def create_coco_gt(gt_store, image_paths):
    """Create COCO GT object from gt_store for real CocoEvaluator integration"""

    # Create COCO structure
    images = []
    annotations = []
    categories = [{'id': i, 'name': ID2NAME[i]} for i in sorted(ID2NAME.keys())]

    ann_id = 1
    img_id_map = {}  # filename -> img_id mapping

    for img_id, img_path in enumerate(image_paths):
        img_name = Path(img_path).name
        img_id_map[img_name] = img_id

        # Load image to get dimensions
        img = Image.open(str(img_path)).convert('RGB')
        w, h = img.size

        images.append({
            'id': img_id,
            'width': w,
            'height': h,
            'file_name': img_name
        })

        # Add annotations for this image
        gts = gt_store.get(img_name, [])
        for gt in gts:
            box = gt['box']  # [x0,y0,x1,y1]
            label = gt['label']  # 1..9
            area = (box[2] - box[0]) * (box[3] - box[1])

            annotations.append({
                'id': ann_id,
                'image_id': img_id,
                'category_id': label,
                'bbox': [box[0], box[1], box[2]-box[0], box[3]-box[1]],  # convert to xywh
                'area': area,
                'iscrowd': 0
            })
            ann_id += 1

    coco_dict = {
        'images': images,
        'annotations': annotations,
        'categories': categories,
        'info': {'description': 'generated by rt-detrv2'}
    }

    coco_gt = COCO()
    coco_gt.dataset = coco_dict
    coco_gt.createIndex()
    return coco_gt, img_id_map


# ========= Main =========
def main(args):
    cfg = YAMLConfig(args.config, resume=args.resume)
    if not args.resume:
        raise AttributeError('Only support resume to load model.state_dict by now.')
    checkpoint = torch.load(args.resume, map_location='cpu')
    state = checkpoint['ema']['module'] if 'ema' in checkpoint else checkpoint['model']
    cfg.model.load_state_dict(state)

    out_root = Path(args.out_folder)
    out_root.mkdir(parents=True, exist_ok=True)
    metrics_dir = out_root / 'metrics'
    metrics_dir.mkdir(parents=True, exist_ok=True)

    # Dentro de main(), definición del wrapper -----------------------------
    class Model(nn.Module):
        def __init__(self) -> None:
            super().__init__()
            # 1) Modo deploy (inferencia pura: sin denoising, sin necesitar targets)
            self.model = cfg.model.deploy()
            self.model.eval()
            self.postprocessor = cfg.postprocessor.deploy()

            # 2) Garantiza eval
            self.model.to(args.device)

        def forward(self, images, orig_target_sizes):
            # 3) Inferencia sin gradientes
            with torch.inference_mode():
                # OJO: muchas implementaciones deploy YA devuelven (labels, boxes, scores)
                # Si es tu caso, quita la línea del postprocessor o deja ambas según tu deploy.
                outputs = self.model(images)  # -> típicamente logits/raw o directamente (labels, boxes, scores)
                outputs = self.postprocessor(outputs, orig_target_sizes)
                return outputs


    model = Model().to(args.device)

    eval_h, eval_w = _resolve_eval_size(args, cfg)
    transforms = T.Compose([T.Resize((eval_h, eval_w)), T.ToTensor(), ])
    font = load_font(args.font_size)

    seg_folder = Path(args.seg_folder) if args.seg_folder else _infer_seg_folder_from_images(args.im_folder)
    if seg_folder is None:
        print("[WARN] Could not infer --seg-folder from --im-folder; GT overlay disabled.")

    im_list = _collect_images(args.im_folder)

    # Limit number of images if requested
    if getattr(args, 'max_images', 0) and args.max_images > 0:
        im_list = im_list[:args.max_images]
        print(f"[INFO] Processing only {len(im_list)} images due to --max-images.")

    # Stores for metrics
    gt_store: Dict[str, List[Dict]] = {}
    pred_store: Dict[str, List[Dict]] = {}
    images_with_any_tp = set()
    status_totals = {c: {'found': 0, 'confused': 0, 'Not_found': 0} for c in CLASSES}

    # Create COCO GT and evaluator for real COCO metrics
    print("[INFO] Creating COCO GT structure for real CocoEvaluator...")
    base_ds, img_id_map = create_coco_gt(gt_store, im_list)  # Create empty first, will populate during loop
    coco_evaluator = CocoEvaluator(base_ds, iou_types=['bbox'])
    coco_results = {}  # Store results for CocoEvaluator

    # First pass: collect all GT data for COCO structure
    print("[INFO] First pass: collecting GT data...")
    for im_file in tqdm(im_list, desc="Collecting GT"):
        img_id = Path(im_file).name

        # read GT
        gt_boxes, gt_labels = [], []
        if seg_folder is not None:
            jpath = _find_gt_json_for_image(Path(im_file), seg_folder)
            if jpath is not None:
                data = _load_json(jpath)
                if isinstance(data, dict):
                    item = data.get(img_id)
                    if isinstance(item, dict):
                        gt_boxes = item.get("boxes", []) or []
                        gt_labels = item.get("labels", []) or []

        # Store GT data
        gt_store[img_id] = [{'box': list(map(float, b)), 'label': int(l)} for b, l in zip(gt_boxes, gt_labels)]



    # --- Sanitize GT: drop unknown labels not present in ID2NAME ---
    VALID_IDS = set(ID2NAME.keys())
    unknown_seen = set()
    for img_name, gts in list(gt_store.items()):
        cleaned = []
        for item in gts:
            lbl = int(item['label'])
            if lbl in VALID_IDS:
                cleaned.append(item)
            else:
                unknown_seen.add(lbl)
        gt_store[img_name] = cleaned

    if unknown_seen:
        print(f"[WARN] Dropping GT with unknown labels (not in ID2NAME): {sorted(unknown_seen)}")
        print("       Tip: añade esos IDs a ID2NAME o corrige los JSON de anotación.")

    # Now create the real COCO GT structure with all data
    print("[INFO] Creating final COCO GT structure...")
    base_ds, img_id_map = create_coco_gt(gt_store, im_list)
    coco_evaluator = CocoEvaluator(base_ds, iou_types=['bbox'])

    # Second pass: inference and evaluation
    print("[INFO] Second pass: running inference and evaluation...")
    for im_file in tqdm(im_list, desc="Inference"):
        img_id = Path(im_file).name
        im_pil = Image.open(str(im_file)).convert('RGB')
        w, h = im_pil.size
        orig_size = torch.tensor([w, h])[None].to(args.device)
        im_data = transforms(im_pil)[None].to(args.device)

        # --- INFERENCIA ---
        labels_pred, boxes_pred, scores_pred = model(im_data, orig_size)

        # GT ya cargado arriba:
        gt_boxes = [g['box'] for g in gt_store[img_id]]
        gt_labels = [g['label'] for g in gt_store[img_id]]

        # Trabajamos SIEMPRE con copias locales y sincronizadas (sin in-place)
        b = boxes_pred[0]
        l = labels_pred[0]
        s = scores_pred[0]

        # 1) Sincroniza longitudes (defensivo)
        n = min(b.shape[0], l.shape[0], s.shape[0])
        if n > 0:
            b = b[:n]
            l = l[:n]
            s = s[:n]

            # 2) Top-K = 100 (COCO maxDets)
            topk = min(100, n)
            idx = torch.argsort(s, descending=True)[:topk]
            b = b[idx]
            l = l[idx]
            s = s[idx]

            # 3) Normaliza etiquetas (0-based/1-based) DESPUÉS de seleccionar
            l = _normalize_pred_labels(l, args.pred_label_base, gt_labels)
        else:
            # tensores vacíos consistentes
            b = torch.empty((0, 4), device=scores_pred.device, dtype=b.dtype)
            l = torch.empty((0,), device=scores_pred.device, dtype=l.dtype)
            s = torch.empty((0,), device=scores_pred.device, dtype=s.dtype)

        # ==== desde aquí usa (b, l, s) para TODO ====

        # pred_store (ya top-100 y sincronizados)
        pred_store[img_id] = [
            {'box': list(map(float, b[i].tolist())),
            'label': int(l[i].item()),
            'score': float(s[i].item())}
            for i in range(b.shape[0])
        ]

        # CocoEvaluator (mismo formato que engine)
        coco_img_id = img_id_map[img_id]
        coco_evaluator.update({
            coco_img_id: {
                "boxes":  b,        # [N,4] xyxy abs
                "scores": s,        # [N]
                "labels": l         # [N] 1..9
            }
        })

        # Visuales / estados por clase
        tmp = im_pil.copy()
        status = paint_gt_and_overlaps(tmp, gt_boxes, gt_labels, b, l, s, args.threshold, args.iou_thr, font)
        if any('found' in st for st in status.values()):
            images_with_any_tp.add(img_id)

        # Guardados por clase
        for cls, statuses in status.items():
            for st in statuses:
                if st in status_totals.get(cls, {}):
                    status_totals[cls][st] += 1
            view = render_filtered_view(im_pil, cls, gt_boxes, gt_labels, b, l, s,
                                        args.threshold, args.iou_thr, font)
            for st in sorted(statuses):
                sub = out_root / cls / st
                sub.mkdir(parents=True, exist_ok=True)
                view.save(str(sub / img_id))

        # Wrong predictions (usa b,l,s)
        pmask_all = s >= args.threshold
        boxes_all  = b[pmask_all]
        labels_all = l[pmask_all]
        scores_all = s[pmask_all]
        # ... (resto idéntico)

 

    # ===================== REAL COCO EVALUATION =====================
    print("[INFO] Running real COCO evaluation...")
    coco_evaluator.synchronize_between_processes()
    coco_evaluator.accumulate()
    coco_evaluator.summarize()

    # Save COCO evaluation results (same as det_engine/det_solver)
    coco_stats = coco_evaluator.coco_eval['bbox'].stats.tolist()
    coco_metrics = {
        'AP@[.50:.95]': coco_stats[0],
        'AP@0.50': coco_stats[1],
        'AP@0.75': coco_stats[2],
        'AP@[.50:.95]_small': coco_stats[3],
        'AP@[.50:.95]_medium': coco_stats[4],
        'AP@[.50:.95]_large': coco_stats[5],
        'AR@[.50:.95]_maxDets=1': coco_stats[6],
        'AR@[.50:.95]_maxDets=10': coco_stats[7],
        'AR@[.50:.95]_maxDets=100': coco_stats[8],
        'AR@[.50:.95]_small': coco_stats[9],
        'AR@[.50:.95]_medium': coco_stats[10],
        'AR@[.50:.95]_large': coco_stats[11]
    }

    # Save COCO metrics
    with (metrics_dir / 'coco_metrics.json').open('w', encoding='utf-8') as f:
        json.dump(coco_metrics, f, indent=2)

    # Save eval.pth (same as det_solver)
    torch.save(coco_evaluator.coco_eval["bbox"].eval, metrics_dir / "eval.pth")
    print(f"[INFO] Real COCO metrics saved to {metrics_dir / 'coco_metrics.json'}")
    print(f"[INFO] COCO eval.pth saved to {metrics_dir / 'eval.pth'}")

    # ===================== METRICS =====================
    # 1) Summary
    n_images = len(im_list)
    n_gts_total = sum(len(v) for v in gt_store.values())
    n_gts_by_class = {c: 0 for c in CLASSES}
    for gts in gt_store.values():
        for g in gts:
            n_gts_by_class[label_to_name(g['label'])] += 1
    pct_images_with_tp = 100.0 * len(images_with_any_tp) / max(1, n_images)

    summary = {
        'n_images': n_images,
        'n_gts_total': n_gts_total,
        'n_gts_by_class': n_gts_by_class,
        'pct_images_with_ge1_TP': pct_images_with_tp,
        'coco_AP_50_95': coco_stats[0],
        'coco_AP_50': coco_stats[1],
    }
    with (metrics_dir / 'summary.json').open('w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)

    # 2) mAP/mAR (COCO-like) with maxDets=100 for AP/AR and IoU [.50:.95] - LEGACY METRICS
    # Note: Real COCO metrics are now available above. These are kept for compatibility.
    iou_list = [0.50] + [round(0.50 + 0.05 * i, 2) for i in range(1, 10)]
    map_res = evaluate_map_mar(gt_store, pred_store, CLASSES, iou_list, mar_k=100, max_dets_ap=100)
    rows = []
    for c in CLASSES + ['_global']:
        rows.append([c,
                     round(map_res['AP@0.50'].get(c, 0.0), 4),
                     round(map_res['AP@[.50:.95]'].get(c, 0.0), 4),
                     round(map_res['AR@0.50@K'].get(c, 0.0), 4),
                     round(map_res['AR@[.50:.95]@K'].get(c, 0.0), 4)])
    write_csv(metrics_dir / 'ap_ar_legacy.csv', rows,
              header=['class', 'AP@0.50', 'AP@[.50:.95]', 'AR@0.50@100', 'AR@[.50:.95]@100'])

    # 2b) Precision/Recall per class a score≥0.5 & IoU≥0.5 (KPI práctico; no COCO)
    pr_data = {}
    prec_recall_rows = []
    scores_tp = []
    scores_fp = []
    for cls in CLASSES:
        gts_cls = []
        for img_id, gts in gt_store.items():
            for g in gts:
                if label_to_name(g['label']) == cls:
                    gts_cls.append((g['box'], img_id))
        npos = len(gts_cls)
        preds_cls = []
        for img_id, preds in pred_store.items():
            preds_img = preds[:100]
            for pi, p in enumerate(preds_img):
                if label_to_name(p['label']) == cls:
                    preds_cls.append((p['score'], p['box'], img_id, (img_id, pi)))
        preds_cls = sorted(preds_cls, key=lambda x: x[0], reverse=True)
        preds_sorted, tp, fp, _ = match_image_one_class(preds_cls, gts_cls, 0.5)
        rec, prec, ap = precision_recall_ap(tp, fp, npos)
        pr_data[cls] = {'rec': rec, 'prec': prec}
        tp_thr = sum(t for (t, (s, _, _, _)) in zip(tp, preds_sorted) if s >= 0.5)
        fp_thr = sum(f for (f, (s, _, _, _)) in zip(fp, preds_sorted) if s >= 0.5)
        fn_thr = max(0, npos - tp_thr)
        precision = tp_thr / max(1, tp_thr + fp_thr)
        recall = tp_thr / max(1, npos)
        prec_recall_rows.append([cls, npos, tp_thr, fp_thr, fn_thr,
                                 round(precision, 4), round(recall, 4)])
        for (t, (s, _, _, _)) in zip(tp, preds_sorted):
            if s >= 0.5:
                (scores_tp if t == 1 else scores_fp).append(s)
    write_csv(metrics_dir / 'precision_recall_at_0.5.csv', prec_recall_rows,
              header=['class', 'GT', 'TP@0.5', 'FP@0.5', 'FN@0.5', 'Precision@0.5', 'Recall@0.5'])
    safe_plot_pr(metrics_dir / 'pr_curves', pr_data)

    # 3) Human-focused TP-rate & global micro TP/GT (COCO-like matching)
    analysis = match_for_analysis(gt_store, pred_store, iou_thr=args.iou_thr)
    tp_rate_rows = []
    for cls in CLASSES:
        n_img_with_gt = analysis['images_with_gt_by_class'][cls]
        n_img_with_tp = analysis['images_with_tp_by_class'][cls]
        tp_per_img = 100.0 * (n_img_with_tp / max(1, n_img_with_gt))
        tp_rate_rows.append([cls, n_img_with_gt, n_img_with_tp, round(tp_per_img, 2)])
    write_csv(metrics_dir / 'tp_rate_by_image.csv', tp_rate_rows,
              header=['class', 'images_with_GT', 'images_with_>=1_TP', 'TP-per-image[%]'])
    tp_total = sum(v for (gt, pd), v in analysis['confusion'].items() if gt == pd and gt != '_extra')
    global_micro = tp_total / max(1, n_gts_total)
    write_csv(metrics_dir / 'tp_micro.csv', [[round(global_micro, 4)]],
              header=['TP/GT (micro, IoU>=0.5, score: full range, maxDets=100)'])

    # 4) Confusion matrix (COCO-like matching)
    safe_plot_confusion(metrics_dir, analysis['confusion'], CLASSES, n_gts_by_class)

    # 5) Error analysis (FN%, FP/imagen, misclasificación%, duplicados%) con matching COCO-like
    dup_counter = Counter()
    for cls in CLASSES:
        preds, gts = [], []
        for img_id, g in gt_store.items():
            for item in g:
                if label_to_name(item['label']) == cls:
                    gts.append((item['box'], img_id))
        for img_id, p in pred_store.items():
            preds_img = p[:100]
            for idx, item in enumerate(preds_img):
                if label_to_name(item['label']) == cls:
                    preds.append((item['score'], item['box'], img_id, (img_id, idx)))
        preds, tp, fp, matches = match_image_one_class(preds, gts, 0.5)
        dup = 0
        for (s, b, im, idx), f in zip(preds, fp):
            if f == 1:
                ious = [iou_rect(b, gb) for gb, iid in gts if iid == im]
                if ious and max(ious) >= 0.5:
                    dup += 1
        dup_counter[cls] += dup

    err_rows = []
    fp_per_img = sum(analysis['fp_by_class'].values()) / max(1, n_images)
    for cls in CLASSES:
        fn = analysis['fn_by_class'][cls]
        fn_pct = 100.0 * fn / max(1, n_gts_by_class[cls])
        mis = sum(v for (gt, pd), v in analysis['confusion'].items() if gt == cls and pd not in (cls, '_missed'))
        assigned = sum(v for (gt, pd), v in analysis['confusion'].items() if gt == cls and pd != '_missed')
        mis_pct = 100.0 * mis / max(1, assigned)
        dup_pct = 100.0 * dup_counter[cls] / max(1, n_gts_by_class[cls])
        err_rows.append([cls,
                         round(fn_pct, 2),
                         round(analysis['fp_by_class'][cls] / max(1, n_images), 4),
                         round(mis_pct, 2),
                         round(dup_pct, 2)])
    write_csv(metrics_dir / 'errors.csv', err_rows,
              header=['class', 'FN[%]', 'FP/imagen', 'Misclasificación[%]', 'Duplicados[%]'])

    # 6) Geometric quality: IoU stats of TP
    iou_rows = []
    import numpy as np
    for cls in CLASSES:
        vals = np.array(analysis['tp_iou_list_by_class'].get(cls, []), dtype=float)
        if vals.size == 0:
            iou_rows.append([cls, 'nan', 'nan', 'nan'])
        else:
            p25, med, p75 = np.percentile(vals, [25, 50, 75]).tolist()
            iou_rows.append([cls, round(med, 3), round(p25, 3), round(p75, 3)])
    write_csv(metrics_dir / 'iou_tp_stats.csv', iou_rows, header=['class', 'IoU_median', 'IoU_p25', 'IoU_p75'])

    # 7) Calibration + sweep (dejo el sweep tal cual; diagnóstico adicional)
    safe_hist_calibration(metrics_dir / 'calibration', scores_tp, scores_fp)
    sweep_rows = []
    for thr in [0.3, 0.5, 0.7]:
        # NOTE: sweep usa matching COCO-like, pero filtrando por score>=thr (diagnóstico no-COCO)
        # Mantengo esto como análisis práctico adicional.
        # Para alinear aún más a COCO, podría eliminar el filtro por score en sweep.
        an = match_for_analysis(gt_store, {im: [p for p in preds if p['score'] >= thr][:100]
                                           for im, preds in pred_store.items()},
                                iou_thr=0.5)
        tp_per_img_avg = []
        for cls in CLASSES:
            n_gt_img = an['images_with_gt_by_class'][cls]
            n_tp_img = an['images_with_tp_by_class'][cls]
            tp_per_img_avg.append(n_tp_img / max(1, n_gt_img))
        tp_rate_mean = sum(tp_per_img_avg) / len(tp_per_img_avg) if tp_per_img_avg else 0.0
        fp_total = sum(an['fp_by_class'].values())
        sweep_rows.append([thr, round(tp_rate_mean, 4), round(fp_total / max(1, n_images), 4)])
    write_csv(metrics_dir / 'sweep.csv', sweep_rows, header=['score_thr', 'TP-rate_img_mean', 'FP/imagen'])

    # 8) Visual quick deliverables (examples)
    examples_dir = metrics_dir / 'examples'
    examples_dir.mkdir(parents=True, exist_ok=True)

    # Top-10 TP by score (COCO-like TP at IoU=0.5)
    tp_candidates = []
    for cls in CLASSES:
        preds_cls, gts_cls = [], []
        for img_id, gts in gt_store.items():
            for g in gts:
                if label_to_name(g['label']) == cls:
                    gts_cls.append((g['box'], img_id))
        for img_id, preds in pred_store.items():
            preds_img = preds[:100]
            for pi, p in enumerate(preds_img):
                if label_to_name(p['label']) == cls:
                    preds_cls.append((p['score'], p['box'], img_id, (img_id, pi)))
        preds_sorted, tp, fp, mt = match_image_one_class(sorted(preds_cls, key=lambda x: x[0], reverse=True), gts_cls, 0.5)
        for (s, b, im, _), t in zip(preds_sorted, tp):
            if t == 1:
                tp_candidates.append((s, im))
    tp_candidates = sorted(list({(s, im) for s, im in tp_candidates}), key=lambda x: x[0], reverse=True)[:10]
    for s, im in tp_candidates:
        img_path = next(p for p in im_list if p.name == im)
        im_pil = Image.open(str(img_path)).convert('RGB')
        _ = paint_gt_and_overlaps(im_pil,
                                  [g['box'] for g in gt_store[im]],
                                  [g['label'] for g in gt_store[im]],
                                  torch.tensor([p['box'] for p in pred_store[im]], dtype=torch.float32),
                                  torch.tensor([p['label'] for p in pred_store[im]], dtype=torch.int64),
                                  torch.tensor([p['score'] for p in pred_store[im]], dtype=torch.float32),
                                  args.threshold, args.iou_thr, font)
        im_pil.save(str(examples_dir / ('TP_' + im)))

    # 10 worst by FN+FP (approx)
    from collections import defaultdict as _dd
    img_to_errors = _dd(int)
    for im in gt_store.keys():
        gts = gt_store[im]
        preds = pred_store.get(im, [])[:100]
        used_pred = set()
        fn_for_im = 0
        for gi, g in enumerate(gts):
            best_i, best_j = 0.0, -1
            for j, p in enumerate(preds):
                if j in used_pred:
                    continue
                iou = iou_rect(g['box'], p['box'])
                if iou > best_i:
                    best_i, best_j = iou, j
            if best_i >= 0.5 and best_j >= 0:
                used_pred.add(best_j)
            else:
                fn_for_im += 1
        fp_for_im = len([j for j in range(len(preds)) if j not in used_pred])
        img_to_errors[im] = fn_for_im + fp_for_im
    worst = sorted(img_to_errors.items(), key=lambda x: x[1], reverse=True)[:10]
    for im, _cnt in worst:
        img_path = next(p for p in im_list if p.name == im)
        im_pil = Image.open(str(img_path)).convert('RGB')
        _ = paint_gt_and_overlaps(im_pil,
                                  [g['box'] for g in gt_store[im]],
                                  [g['label'] for g in gt_store[im]],
                                  torch.tensor([p['box'] for p in pred_store[im]], dtype=torch.float32),
                                  torch.tensor([p['label'] for p in pred_store[im]], dtype=torch.int64),
                                  torch.tensor([p['score'] for p in pred_store[im]], dtype=torch.float32),
                                  args.threshold, args.iou_thr, font)
        im_pil.save(str(examples_dir / ('FAIL_' + im)))

    # Per class: 3 hits + 3 errors
    ex_by_cls = metrics_dir / 'examples_by_class'
    ex_by_cls.mkdir(parents=True, exist_ok=True)
    for cls in CLASSES:
        cls_dir = ex_by_cls / cls
        cls_dir.mkdir(parents=True, exist_ok=True)
        # Hits: top-3 TP of the class
        preds_cls, gts_cls = [], []
        for img_id, gts in gt_store.items():
            for g in gts:
                if label_to_name(g['label']) == cls:
                    gts_cls.append((g['box'], img_id))
        for img_id, preds in pred_store.items():
            preds_img = preds[:100]
            for i, p in enumerate(preds_img):
                if label_to_name(p['label']) == cls:
                    preds_cls.append((p['score'], p['box'], img_id, (img_id, i)))
        preds_sorted, tp, fp, mt = match_image_one_class(sorted(preds_cls, key=lambda x: x[0], reverse=True), gts_cls, 0.5)
        tp_imgs = [im for (s, b, im, _), t in zip(preds_sorted, tp) if t == 1][:3]
        for im in tp_imgs:
            img_path = next(p for p in im_list if p.name == im)
            im_pil = Image.open(str(img_path)).convert('RGB')
            view = render_filtered_view(im_pil, cls,
                                        [g['box'] for g in gt_store[im]],
                                        [g['label'] for g in gt_store[im]],
                                        torch.tensor([p['box'] for p in pred_store[im]], dtype=torch.float32),
                                        torch.tensor([p['label'] for p in pred_store[im]], dtype=torch.int64),
                                        torch.tensor([p['score'] for p in pred_store[im]], dtype=torch.float32),
                                        args.threshold, args.iou_thr, font)
            view.save(str(cls_dir / ('OK_' + im)))
        # Errors: 3 cases with FN (miss) or confusion
        bad_imgs = []
        for im in gt_store.keys():
            has_cls = any(label_to_name(g['label']) == cls for g in gt_store[im])
            if not has_cls:
                continue
            preds = [p for p in pred_store[im][:100] if label_to_name(p['label']) == cls]
            gts = [g for g in gt_store[im] if label_to_name(g['label']) == cls]
            matched = False
            # check any GT matched by class with IoU>=0.5
            for g in gts:
                if any(iou_rect(g['box'], p['box']) >= 0.5 for p in preds):
                    matched = True
                    break
            if not matched:
                bad_imgs.append(im)
        for im in bad_imgs[:3]:
            img_path = next(p for p in im_list if p.name == im)
            img = Image.open(str(img_path)).convert('RGB')
            view = render_filtered_view(img, cls,
                                        [g['box'] for g in gt_store[im]],
                                        [g['label'] for g in gt_store[im]],
                                        torch.tensor([p['box'] for p in pred_store[im]], dtype=torch.float32),
                                        torch.tensor([p['label'] for p in pred_store[im]], dtype=torch.int64),
                                        torch.tensor([p['score'] for p in pred_store[im]], dtype=torch.float32),
                                        args.threshold, args.iou_thr, font)
            view.save(str(cls_dir / ('ERR_' + im)))

    # 9) Final table (compat)
    pr_index = {r[0]: r for r in prec_recall_rows}
    iou_index = {r[0]: r for r in iou_rows}
    err_index = {r[0]: r for r in err_rows}
    final_rows = []
    for cls in CLASSES:
        gt = n_gts_by_class[cls]
        n_img_with_gt = analysis['images_with_gt_by_class'][cls]
        n_img_with_tp = analysis['images_with_tp_by_class'][cls]
        tp_img_pct = 100.0 * n_img_with_tp / max(1, n_img_with_gt)
        AP50 = map_res['AP@0.50'].get(cls, 0.0)
        R05 = float(pr_index.get(cls, [None] * 7)[6] or 0.0)
        IoUmed = iou_index.get(cls, [None, 'nan'])[1]
        FPimg = err_index.get(cls, [None, None, 0.0])[2]
        FNpct = err_index.get(cls, [None, 0.0])[1]
        final_rows.append([cls, gt, round(tp_img_pct, 2), round(AP50, 4), round(R05, 4), IoUmed, FPimg, FNpct])

    fp_per_img = sum(analysis['fp_by_class'].values()) / max(1, n_images)
    global_row = ['Global',
                  n_gts_total,
                  round(sum(r[2] for r in final_rows) / max(1, len(CLASSES)), 2),
                  round(map_res['AP@0.50']['_global'], 4),
                  round(sum(float(pr_index.get(c, [None] * 7)[6] or 0.0) for c in CLASSES) / max(1, len(CLASSES)), 4),
                  '—',
                  round(fp_per_img, 4),
                  round(100.0 * sum(analysis['fn_by_class'][c] for c in CLASSES) / max(1, n_gts_total), 2)]
    final_rows.append(global_row)
    write_csv(metrics_dir / 'final_table.csv', final_rows,
              header=['class', '#GT', 'TP-por-imagen[%]', 'mAP@0.50', 'Recall@0.5', 'IoU_mediana', 'FP/imagen', 'FN[%]'])

    # ===== Single JSON with everything =====
    ap_ar_table = {
        row[0]: {
            'AP@0.50': row[1],
            'AP@[.50:.95]': row[2],
            'AR@0.50@100': row[3],
            'AR@[.50:.95]@100': row[4],
        } for row in rows
    }

    precision_recall_table = {
        r[0]: {
            'GT': r[1], 'TP@0.5': r[2], 'FP@0.5': r[3], 'FN@0.5': r[4],
            'Precision@0.5': r[5], 'Recall@0.5': r[6]
        } for r in prec_recall_rows
    }

    tp_rate_table = {
        r[0]: {
            'images_with_GT': r[1],
            'images_with_>=1_TP': r[2],
            'TP-per-image[%]': r[3]
        } for r in tp_rate_rows
    }

    errors_table = {
        r[0]: {
            'FN[%]': r[1],
            'FP/imagen': r[2],
            'Misclasificación[%]': r[3],
            'Duplicados[%]': r[4],
        } for r in err_rows
    }

    iou_stats_table = {
        r[0]: {
            'IoU_median': r[1],
            'IoU_p25': r[2],
            'IoU_p75': r[3],
        } for r in iou_rows
    }

    sweep_table = [
        {'score_thr': s[0], 'TP-rate_img_mean': s[1], 'FP/imagen': s[2]}
        for s in sweep_rows
    ]

    final_table_dict = {
        r[0]: {
            '#GT': r[1],
            'TP-por-imagen[%]': r[2],
            'mAP@0.50': r[3],
            'Recall@0.5': r[4],
            'IoU_mediana': r[5],
            'FP/imagen': r[6],
            'FN[%]': r[7],
        } for r in final_rows
    }

    all_json = {
        'summary': summary,
        'status_totals': status_totals,
        'coco_metrics_real': coco_metrics,  # Real COCO metrics from CocoEvaluator
        'map_mar': map_res,  # Legacy metrics for comparison
        'ap_ar_table': ap_ar_table,
        'precision_recall_at_0.5': precision_recall_table,
        'tp_rate_by_image': tp_rate_table,
        'tp_micro_global': global_micro,
        'errors': errors_table,
        'iou_tp_stats': iou_stats_table,
        'calibration': {
            'scores_tp_count': len(scores_tp),
            'scores_fp_count': len(scores_fp),
        },
        'sweep': sweep_table,
        'final_table': final_table_dict,
        'config': {
            'threshold': args.threshold,
            'iou_thr_visuals_and_diag': args.iou_thr,
            'eval_size': [eval_h, eval_w],
            'device': args.device,
            'max_images': args.max_images,
            'pred_label_base_used': args.pred_label_base,
            'maxDets': 100,
            'metrics_score_threshold': 'none (full range, COCO-like)',
            'coco_evaluator_integrated': True
        }
    }
    with (metrics_dir / 'all_metrics.json').open('w', encoding='utf-8') as f:
        json.dump(all_json, f, indent=2)

    # ===== Single wide CSV =====
    big_header = [
        'class',
        '#GT',
        'images_with_GT', 'images_with_>=1_TP', 'TP-per-image[%]',
        'AP@0.50', 'AP@[.50:.95]', 'AR@0.50@100', 'AR@[.50:.95]@100',
        'Precision@0.5', 'Recall@0.5',
        'IoU_median', 'IoU_p25', 'IoU_p75',
        'FP/imagen', 'FN[%]', 'Misclasificación[%]', 'Duplicados[%]',
        'found_count', 'confused_count', 'Not_found_count'
    ]
    big_rows = []
    for cls in CLASSES:
        gt = n_gts_by_class[cls]
        imgs_gt = analysis['images_with_gt_by_class'][cls]
        imgs_tp = analysis['images_with_tp_by_class'][cls]
        tp_img_pct = 100.0 * imgs_tp / max(1, imgs_gt)
        AP50 = map_res['AP@0.50'].get(cls, 0.0)
        APm  = map_res['AP@[.50:.95]'].get(cls, 0.0)
        AR50 = map_res['AR@0.50@K'].get(cls, 0.0)
        ARm  = map_res['AR@[.50:.95]@K'].get(cls, 0.0)
        prec05 = precision_recall_table.get(cls, {}).get('Precision@0.5', 0.0)
        rec05  = precision_recall_table.get(cls, {}).get('Recall@0.5', 0.0)
        iou_m  = iou_stats_table.get(cls, {}).get('IoU_median', 'nan')
        iou_p25= iou_stats_table.get(cls, {}).get('IoU_p25', 'nan')
        iou_p75= iou_stats_table.get(cls, {}).get('IoU_p75', 'nan')
        fpimg  = errors_table.get(cls, {}).get('FP/imagen', 0.0)
        fnpct  = errors_table.get(cls, {}).get('FN[%]', 0.0)
        mispct = errors_table.get(cls, {}).get('Misclasificación[%]', 0.0)
        duppct = errors_table.get(cls, {}).get('Duplicados[%]', 0.0)
        st = status_totals.get(cls, {'found':0,'confused':0,'Not_found':0})
        big_rows.append([
            cls, gt, imgs_gt, imgs_tp, round(tp_img_pct, 2),
            round(AP50,4), round(APm,4), round(AR50,4), round(ARm,4),
            prec05, rec05, iou_m, iou_p25, iou_p75,
            fpimg, fnpct, mispct, duppct,
            st['found'], st['confused'], st['Not_found']
        ])

    big_rows.append([
        'Global',
        n_gts_total,
        sum(analysis['images_with_gt_by_class'][c] for c in CLASSES),
        sum(analysis['images_with_tp_by_class'][c] for c in CLASSES),
        round(sum(r[4] for r in big_rows if isinstance(r[4], (int,float))) / max(1, len(CLASSES)), 2),
        round(map_res['AP@0.50']['_global'],4),
        round(map_res['AP@[.50:.95]']['_global'],4),
        round(map_res['AR@0.50@K']['_global'],4),
        round(map_res['AR@[.50:.95]@K']['_global'],4),
        round(sum(precision_recall_table.get(c, {}).get('Precision@0.5', 0.0) for c in CLASSES) / max(1, len(CLASSES)), 4),
        round(sum(precision_recall_table.get(c, {}).get('Recall@0.5', 0.0) for c in CLASSES) / max(1, len(CLASSES)), 4),
        '—','—','—',
        round(fp_per_img,4),
        round(100.0 * sum(analysis['fn_by_class'][c] for c in CLASSES) / max(1, n_gts_total), 2),
        '—','—',
        sum(status_totals[c]['found'] for c in CLASSES),
        sum(status_totals[c]['confused'] for c in CLASSES),
        sum(status_totals[c]['Not_found'] for c in CLASSES),
    ])

    write_csv(metrics_dir / 'all_metrics.csv', big_rows, header=big_header)

    # Sanity check 'found'
    total_found = sum(status_totals[c]['found'] for c in CLASSES)
    if total_found == 0:
        try:
            some_pred_labels = []
            for v in pred_store.values():
                some_pred_labels += [p['label'] for p in v]
            uniq_preds = sorted(list(set(some_pred_labels)))[:15]
        except Exception:
            uniq_preds = []
        try:
            some_gt_labels = []
            for v in gt_store.values():
                some_gt_labels += [g['label'] for g in v]
            uniq_gts = sorted(list(set(some_gt_labels)))[:15]
        except Exception:
            uniq_gts = []
        print("[WARN] No 'found' recorded. Possible label-index mismatch (0-based vs 1-based).")
        print(f"       Pred labels seen (sample): {uniq_preds}")
        print(f"       GT labels seen (sample):   {uniq_gts}")
    else:
        print("[INFO] Per-class counts (found/confused/Not_found):")
        for c in CLASSES:
            ft = status_totals[c]
            print(f"  - {c}: found={ft['found']} confused={ft['confused']} Not_found={ft['Not_found']}")

    print(f"\n=== REAL COCO METRICS (same as det_engine) ===")
    print(f"  - COCO AP@[.50:.95]: {coco_stats[0]:.4f}")
    print(f"  - COCO AP@0.50: {coco_stats[1]:.4f}")
    print(f"  - COCO AP@0.75: {coco_stats[2]:.4f}")
    print(f"  - COCO AR@[.50:.95]_maxDets=100: {coco_stats[8]:.4f}")
    print(f"\n=== LEGACY METRICS (for comparison) ===")
    print(f"  - Legacy AP@0.50: {round(map_res['AP@0.50'].get('_global', 0.0), 4)}")
    print(f"  - Legacy AP@[.50:.95]: {round(map_res['AP@[.50:.95]'].get('_global', 0.0), 4)}")
    print(f"  - Images with >=1 TP: {round(pct_images_with_tp, 2)}%")
    print(f"  - Global TP/GT (micro): {round(global_micro, 4)}")
    print(f"  - FP per image: {round(fp_per_img, 4)}")
    print(f"\n[OK] Metrics and visuals saved under: {metrics_dir}")
    print(f"[OK] Real COCO metrics saved to: {metrics_dir / 'coco_metrics.json'}")
    print(f"[OK] COCO eval.pth saved to: {metrics_dir / 'eval.pth'}")


if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('-c', '--config', type=str, required=True)
    parser.add_argument('-r', '--resume', type=str, required=True)
    parser.add_argument('-f', '--im-folder', '--im_folder', dest='im_folder', type=str, required=True)
    parser.add_argument('-o', '--out-folder', '--out_folder', dest='out_folder', type=str, required=True)
    parser.add_argument('-t', '--threshold', type=float, default=0.5)
    parser.add_argument('-d', '--device', type=str, default='cpu')
    parser.add_argument('--eval-size', type=int, nargs=2, metavar=('H', 'W'))
    parser.add_argument('--seg-folder', '--seg_folder', dest='seg_folder', type=str, default=None)
    parser.add_argument('--iou-thr', type=float, default=0.5, help='IoU used for visuals and diagnostics (confusion, errors).')
    parser.add_argument('--font-size', type=int, default=22)
    parser.add_argument('--max-images', type=int, default=0, help='If >0, process only the first N images.')
    parser.add_argument('--pred-label-base', choices=['auto', '0', '1'], default='auto',
                        help="Prediction label index base: '0' if model returns [0..8], '1' if [1..9], 'auto' to infer.")
    args = parser.parse_args()
    main(args)
