__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetrv2_r50vd.yml',
]


output_dir: ./output/rtdetrv2_hgnetv2_l_6x_coco


RTDETR:
  backbone: HGNetv2


HGNetv2:
  name: 'L'
  return_idx: [1, 2, 3]
  freeze_at: 0
  freeze_norm: True
  pretrained: True


optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*norm|bn).*$'
      lr: 0.000005
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

