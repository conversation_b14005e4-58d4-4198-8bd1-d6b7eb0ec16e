/home/<USER>/miniconda3/envs/DETRv2/bin/python: can't open file '/mnt/data/projects/RT-DETRv2_ObjectDetection/Medical/RT-DETRv2/configs/rtdetrv2/tools/train.py': [Errno 2] No such file or directory
[2025-10-15 20:51:41,848] torch.distributed.elastic.multiprocessing.api: [ERROR] failed (exitcode: 2) local_rank: 0 (pid: 2523193) of binary: /home/<USER>/miniconda3/envs/DETRv2/bin/python
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/envs/DETRv2/bin/torchrun", line 33, in <module>
    sys.exit(load_entry_point('torch==2.2.2', 'console_scripts', 'torchrun')())
  File "/home/<USER>/miniconda3/envs/DETRv2/lib/python3.9/site-packages/torch/distributed/elastic/multiprocessing/errors/__init__.py", line 347, in wrapper
    return f(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/DETRv2/lib/python3.9/site-packages/torch/distributed/run.py", line 812, in main
    run(args)
  File "/home/<USER>/miniconda3/envs/DETRv2/lib/python3.9/site-packages/torch/distributed/run.py", line 803, in run
    elastic_launch(
  File "/home/<USER>/miniconda3/envs/DETRv2/lib/python3.9/site-packages/torch/distributed/launcher/api.py", line 135, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/home/<USER>/miniconda3/envs/DETRv2/lib/python3.9/site-packages/torch/distributed/launcher/api.py", line 268, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
============================================================
tools/train.py FAILED
------------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
------------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-10-15_20:51:41
  host      : gpu05
  rank      : 0 (local_rank: 0)
  exitcode  : 2 (pid: 2523193)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
============================================================
