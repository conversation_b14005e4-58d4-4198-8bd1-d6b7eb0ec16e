__include__: [
  '../dataset/medical_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetrv2_r50vd.yml',
]


output_dir: /mnt/ssd-nvme1n1p1/dataset/object_detection_model/checkpoint_30_10_2025/rtdetrv2_r50vd_6x_Medical_1024



optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*norm).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

