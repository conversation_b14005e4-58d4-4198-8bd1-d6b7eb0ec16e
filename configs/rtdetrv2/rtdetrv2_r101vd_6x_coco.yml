__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetrv2_r50vd.yml',
]


output_dir: ./output/rtdetrv2_r101vd_6x_coco


PResNet:
  depth: 101


HybridEncoder:
  # intra
  hidden_dim: 384
  dim_feedforward: 2048


RTDETRTransformerv2:
  feat_channels: [384, 384, 384]


optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*norm|bn).*$'
      lr: 0.000001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bn)).*$'
      weight_decay: 0.

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

