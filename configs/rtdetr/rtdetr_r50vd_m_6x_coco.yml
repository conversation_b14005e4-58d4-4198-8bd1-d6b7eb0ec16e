__include__: [
  '../dataset/coco_detection.yml',
  '../runtime.yml',
  './include/dataloader.yml',
  './include/optimizer.yml',
  './include/rtdetr_r50vd.yml',
]

output_dir: ./output/rtdetr_r50vd_m_6x_coco


HybridEncoder:
  expansion: 0.5


RTDETRTransformer:
  eval_idx: 2 # use 3th decoder layer to eval



optimizer:
  type: AdamW
  params: 
    - 
      params: '^(?=.*backbone)(?!.*norm|bn).*$'
      lr: 0.00001
    - 
      params: '^(?=.*(?:encoder|decoder))(?=.*(?:norm|bn|bias)).*$'
      weight_decay: 0.

  lr: 0.0001
  betas: [0.9, 0.999]
  weight_decay: 0.0001

