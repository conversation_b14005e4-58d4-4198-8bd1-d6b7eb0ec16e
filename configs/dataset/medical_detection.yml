task: detection

evaluator:
  type: TextileEvaluator
  iou_types: ['bbox', ]
  iou_thresholds: [0.4]  # Changed from default 0.5:0.95 to 0.4 for textile defect detection


# num_classes: 365
# remap_mscoco_category: False

# num_classes: 91
# remap_mscoco_category: False

num_classes: 9
save_optimizer: True
save_scheduler: True 
save_ema: True

train_dataloader: 
  type: DataLoader
  dataset: 
    type: DefectJSONDetection
    images: /mnt/ssd-nvme1n1p1/dataset/sigvaris_data/split_2/split_20251031/split_dt-detrv2/train/images
    json_dir: /mnt/ssd-nvme1n1p1/dataset/sigvaris_data/split_2/split_20251031/split_dt-detrv2/train/annotations
    class_id_map:
      1: 0
      2: 1
      3: 2
      4: 3
      5: 4
      6: 5
      7: 6
      8: 7
      9: 8
      
      
    transforms:
      type: Compose
      ops: ~
  shuffle: True
  num_workers: 4
  drop_last: True 
  collate_fn:
    type: BatchImageCollateFuncion


val_dataloader:
  type: DataLoader
  dataset: 
    type: DefectJSONDetection
    images: /mnt/ssd-nvme1n1p1/dataset/sigvaris_data/split_2/split_20251031/split_dt-detrv2/test/images
    json_dir: /mnt/ssd-nvme1n1p1/dataset/sigvaris_data/split_2/split_20251031/split_dt-detrv2/test/annotations
    class_id_map:
      1: 0
      2: 1
      3: 2
      4: 3
      5: 4
      6: 5
      7: 6
      8: 7
      9: 8
      

    transforms:
      type: Compose
      ops: ~
  shuffle: False
  num_workers: 4
  drop_last: False
  collate_fn:
    type: BatchImageCollateFuncion
