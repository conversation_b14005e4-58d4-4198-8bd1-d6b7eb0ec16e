task: detection

evaluator:
  type: CocoEvaluator
  iou_types: ['bbox', ]

# num_classes: 365
# remap_mscoco_category: False

# num_classes: 91
# remap_mscoco_category: False

num_classes: 1
save_optimizer: True
save_scheduler: True 
save_ema: True

train_dataloader: 
  type: DataLoader
  dataset: 
    type: DefectJSONDetection
    images: /mnt/data/datasets/talapparel_garmet/stable_dirty_soil_stain_solid_review/closed_evaluation/train_532_img
    json_dir: /mnt/data/datasets/talapparel_garmet/stable_dirty_soil_stain_solid_review/closed_evaluation/train_json
    class_id_map:
      1: 0
    transforms:
      type: Compose
      ops: ~
  shuffle: True
  num_workers: 4
  drop_last: True 
  collate_fn:
    type: BatchImageCollateFuncion


val_dataloader:
  type: DataLoader
  dataset: 
    type: DefectJSONDetection
    images: /mnt/data/datasets/talapparel_garmet/stable_dirty_soil_stain_solid_review/closed_evaluation/test_100_img
    json_dir: /mnt/data/datasets/talapparel_garmet/stable_dirty_soil_stain_solid_review/closed_evaluation/test_json
    class_id_map:
      1: 0
    transforms:
      type: Compose
      ops: ~
  shuffle: False
  num_workers: 4
  drop_last: False
  collate_fn:
    type: BatchImageCollateFuncion
